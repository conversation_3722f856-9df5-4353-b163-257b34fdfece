/**
 * 简化的fetch工具
 * 
 * 替代复杂的HttpClient类，提供轻量级的API请求功能
 * 保留必要的错误处理和类型安全
 */

import { API_CONFIG, buildApiUrl, getApiHeaders, buildQueryString } from '../../config/api/apiConfig';

// ==================== 类型定义 ====================

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  data: T;
  status: number;
}

/**
 * API错误接口
 */
export interface ApiError {
  message: string;
  code: string;
  status?: number;
  originalError?: Error;
}

/**
 * 请求选项接口
 */
export interface FetchOptions extends Omit<RequestInit, 'headers'> {
  /** 自定义请求头 */
  headers?: Record<string, string>;
  /** 查询参数 */
  params?: Record<string, string | number | boolean>;
  /** 请求超时时间（毫秒），默认使用配置中的值 */
  timeout?: number;
}

// ==================== 错误处理 ====================

/**
 * 创建标准化的API错误
 * @param error 原始错误对象
 * @returns 标准化的API错误
 */
export const createApiError = (error: unknown): ApiError => {
  // 如果已经是ApiError，直接返回
  if (isApiError(error)) {
    return error;
  }

  // 处理AbortError（超时）
  if (error instanceof Error && error.name === 'AbortError') {
    return {
      message: '请求超时，请稍后重试',
      code: 'TIMEOUT_ERROR',
      originalError: error
    };
  }

  // 处理网络错误
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      message: '网络连接失败，请检查网络设置',
      code: 'NETWORK_ERROR',
      originalError: error
    };
  }

  // 处理一般Error对象
  if (error instanceof Error) {
    return {
      message: error.message || '请求失败，请稍后重试',
      code: 'FETCH_ERROR',
      originalError: error
    };
  }

  // 处理字符串错误
  if (typeof error === 'string') {
    return {
      message: error,
      code: 'UNKNOWN_ERROR'
    };
  }

  // 兜底处理
  return {
    message: '未知错误，请稍后重试',
    code: 'UNKNOWN_ERROR',
    originalError: error instanceof Error ? error : new Error(String(error))
  };
};

/**
 * 检查是否为ApiError类型
 * @param error 错误对象
 * @returns 是否为ApiError
 */
export const isApiError = (error: unknown): error is ApiError => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    'code' in error &&
    typeof (error as ApiError).message === 'string' &&
    typeof (error as ApiError).code === 'string'
  );
};

/**
 * 根据HTTP状态码创建错误
 * @param status HTTP状态码
 * @param statusText 状态文本
 * @param responseData 响应数据
 * @returns API错误
 */
export const createHttpError = (status: number, statusText: string, responseData?: unknown): ApiError => {
  let message: string;
  let code: string;

  switch (true) {
    case status === 401 || status === 403:
      message = '认证失败，请检查访问权限';
      code = 'AUTH_ERROR';
      break;
    case status >= 500:
      message = '服务器错误，请稍后重试';
      code = 'SERVER_ERROR';
      break;
    case status >= 400:
      message = (responseData && typeof responseData === 'object' && 'message' in responseData && typeof responseData.message === 'string')
        ? responseData.message
        : '请求参数错误';
      code = 'CLIENT_ERROR';
      break;
    default:
      message = `HTTP错误：${status} ${statusText}`;
      code = 'HTTP_ERROR';
  }

  return {
    message,
    code,
    status,
    originalError: new Error(`HTTP ${status}: ${statusText}`)
  };
};

// ==================== 核心fetch函数 ====================

/**
 * 简化的fetch函数
 * 
 * 替代复杂的HttpClient.request方法
 * 提供基础的错误处理、超时控制和类型安全
 * 
 * @param endpoint API端点路径
 * @param options 请求选项
 * @returns Promise<ApiResponse<T>>
 */
export const simpleFetch = async <T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<ApiResponse<T>> => {
  const {
    headers: customHeaders,
    params,
    timeout = API_CONFIG.timeout,
    ...fetchOptions
  } = options;

  // 构建完整URL
  let url = buildApiUrl(endpoint);
  
  // 添加查询参数
  if (params) {
    const queryString = buildQueryString(params);
    url += (url.includes('?') ? '&' : '?') + queryString;
  }

  // 构建请求头
  const headers = getApiHeaders(customHeaders);

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 执行fetch请求
    const response = await fetch(url, {
      headers,
      signal: controller.signal,
      ...fetchOptions
    });

    // 清除超时定时器
    clearTimeout(timeoutId);

    // 检查响应状态
    if (!response.ok) {
      let responseData;
      try {
        responseData = await response.json();
      } catch {
        responseData = await response.text();
      }

      throw createHttpError(response.status, response.statusText, responseData);
    }

    // 解析响应数据
    const data = await response.json();

    return {
      data,
      status: response.status
    };

  } catch (error) {
    // 清除超时定时器
    clearTimeout(timeoutId);
    
    // 抛出标准化错误
    throw createApiError(error);
  }
};

// ==================== 便捷方法 ====================

/**
 * GET请求的便捷方法
 * @param endpoint API端点
 * @param params 查询参数
 * @param options 其他选项
 * @returns Promise<ApiResponse<T>>
 */
export const get = <T>(
  endpoint: string,
  params?: Record<string, string | number | boolean>,
  options?: Omit<FetchOptions, 'method' | 'params'>
): Promise<ApiResponse<T>> => {
  return simpleFetch<T>(endpoint, {
    ...options,
    method: 'GET',
    params
  });
};

/**
 * POST请求的便捷方法
 * @param endpoint API端点
 * @param body 请求体
 * @param options 其他选项
 * @returns Promise<ApiResponse<T>>
 */
export const post = <T>(
  endpoint: string,
  body?: unknown,
  options?: Omit<FetchOptions, 'method' | 'body'>
): Promise<ApiResponse<T>> => {
  return simpleFetch<T>(endpoint, {
    ...options,
    method: 'POST',
    body: body ? JSON.stringify(body) : undefined
  });
};
