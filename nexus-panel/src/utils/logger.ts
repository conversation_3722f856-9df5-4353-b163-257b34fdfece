/**
 * 简化日志工具函数
 * 
 * 提供过渡期使用的日志函数封装，保持与现有LogFunction类型兼容
 * 支持环境变量控制和标准化格式输出
 */

/**
 * 日志级别枚举
 */
export enum LogLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success'
}

/**
 * 日志函数类型（简化版）
 */
export type LogFunction = (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;

/**
 * 日志配置接口
 */
interface LoggerConfig {
  /** 是否启用日志输出 */
  enabled: boolean;
  /** 最小日志级别 */
  minLevel: LogLevel;
  /** 是否包含时间戳 */
  includeTimestamp: boolean;
  /** 模块名称前缀 */
  modulePrefix?: string;
}

/**
 * 默认日志配置
 */
const defaultConfig: LoggerConfig = {
  enabled: true,
  minLevel: process.env.NODE_ENV === 'production' ? LogLevel.WARNING : LogLevel.INFO,
  includeTimestamp: true,
  modulePrefix: undefined
};

/**
 * 日志级别优先级映射
 */
const levelPriority: Record<LogLevel, number> = {
  [LogLevel.INFO]: 0,
  [LogLevel.SUCCESS]: 1,
  [LogLevel.WARNING]: 2,
  [LogLevel.ERROR]: 3
};

/**
 * 格式化日志消息
 */
function formatMessage(level: LogLevel, message: string, modulePrefix?: string): string {
  const timestamp = new Date().toISOString();
  const prefix = modulePrefix ? `[${modulePrefix}]` : '';
  const levelTag = `[${level.toUpperCase()}]`;

  if (defaultConfig.includeTimestamp) {
    return `${timestamp} ${levelTag} ${prefix} ${message}`.trim();
  }

  return `${levelTag} ${prefix} ${message}`.trim();
}

/**
 * 检查日志级别是否应该输出
 */
function shouldLog(level: LogLevel): boolean {
  if (!defaultConfig.enabled) {
    return false;
  }

  return levelPriority[level] >= levelPriority[defaultConfig.minLevel];
}

/**
 * 核心日志函数
 */
function log(level: LogLevel, message: string, details?: unknown, modulePrefix?: string): void {
  if (!shouldLog(level)) {
    return;
  }

  const formattedMessage = formatMessage(level, message, modulePrefix);

  switch (level) {
    case LogLevel.INFO:
    case LogLevel.SUCCESS:
      if (details !== undefined) {
        console.log(formattedMessage, details);
      } else {
        console.log(formattedMessage);
      }
      break;

    case LogLevel.WARNING:
      if (details !== undefined) {
        console.warn(formattedMessage, details);
      } else {
        console.warn(formattedMessage);
      }
      break;

    case LogLevel.ERROR:
      if (details !== undefined) {
        console.error(formattedMessage, details);
      } else {
        console.error(formattedMessage);
      }
      break;
  }
}

/**
 * 创建模块专用的日志函数
 */
export function createModuleLogger(moduleName: string): LogFunction {
  return (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => {
    const logLevel = level === 'success' ? LogLevel.SUCCESS :
      level === 'warning' ? LogLevel.WARNING :
        level === 'error' ? LogLevel.ERROR : LogLevel.INFO;

    log(logLevel, message, details, moduleName);
  };
}

/**
 * 通用日志函数（兼容原LogFunction接口）
 */
export const logger: LogFunction = (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => {
  const logLevel = level === 'success' ? LogLevel.SUCCESS :
    level === 'warning' ? LogLevel.WARNING :
      level === 'error' ? LogLevel.ERROR : LogLevel.INFO;

  log(logLevel, message, details);
};

/**
 * 便捷的日志方法
 */
export const Logger = {
  /**
   * 信息日志
   */
  info: (message: string, details?: unknown, module?: string) => {
    log(LogLevel.INFO, message, details, module);
  },

  /**
   * 成功日志
   */
  success: (message: string, details?: unknown, module?: string) => {
    log(LogLevel.SUCCESS, message, details, module);
  },

  /**
   * 警告日志
   */
  warn: (message: string, details?: unknown, module?: string) => {
    log(LogLevel.WARNING, message, details, module);
  },

  /**
   * 错误日志
   */
  error: (message: string, details?: unknown, module?: string) => {
    log(LogLevel.ERROR, message, details, module);
  },

  /**
   * 配置日志系统
   */
  configure: (config: Partial<LoggerConfig>) => {
    Object.assign(defaultConfig, config);
  },

  /**
   * 获取当前配置
   */
  getConfig: (): LoggerConfig => ({ ...defaultConfig })
};

/**
 * 预定义的模块日志器
 */
export const ModuleLoggers = {
  API: createModuleLogger('API'),
  MQTT: createModuleLogger('MQTT'),
  UI: createModuleLogger('UI'),
  Memory: createModuleLogger('内存管理'),
  Navigation: createModuleLogger('导航'),
  Configuration: createModuleLogger('配置')
};

/**
 * 导出默认日志函数（向后兼容）
 */
export default logger;

/**
 * 快速迁移助手 - 直接替换addConsoleLog调用
 */
export const addConsoleLog = logger;

// 开发环境下的配置提示
if (process.env.NODE_ENV === 'development') {
  console.log('[Logger] 日志系统已初始化', {
    minLevel: defaultConfig.minLevel,
    enabled: defaultConfig.enabled,
    environment: process.env.NODE_ENV
  });
}
