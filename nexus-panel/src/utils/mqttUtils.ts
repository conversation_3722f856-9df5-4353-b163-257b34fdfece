/**
 * MQTT 工具函数
 * 
 * 简化的MQTT业务逻辑工具函数，替代复杂的useMQTTIntegration
 * 将业务消息映射和发送逻辑转换为纯函数
 */

import {
    MQTTUtils,
    MQTTDomain,
    MQTTContext,
    MQTTAction,
    MQTTTarget,
    type MQTTService
} from '../services/mqtt';

/**
 * 导航到 MQTT 消息的映射配置
 */
export const NAVIGATION_MQTT_MAPPING = {
    // 规则显示映射
    rules: {
        qa: { id: 1, name: "有问必答规则" },
        onestation: { id: 2, name: "一站到底规则" },
        timerace: { id: 3, name: "争分夺秒规则" },
        finalpk: { id: 4, name: "终极PK规则" },
        tiebreak: { id: 5, name: "同分加赛规则" },
        scoring: { id: 6, name: "积分办法" },
    },
    // 环节切换映射
    sessions: {
        qa: { id: 1, name: "有问必答" },
        onestation: { id: 3, name: "一站到底" },
        timerace: { id: 4, name: "争分夺秒" },
        finalpk: { id: 7, name: "终极PK" },
        tiebreak: { id: 5, name: "同分加赛" },
        ranking: { id: 8, name: "总分排名" },
    }
} as const;

/**
 * 项目到赛事的映射配置
 */
export const PROJECT_RACE_MAPPING = ["", "功能测试", "第一阶段", "第二阶段", "决赛阶段"];

/**
 * 发送项目切换消息
 * 
 * @param mqttService MQTT服务实例
 * @param projectId 项目ID
 * @param raceName 赛事名称
 */
export function sendProjectChangeMessage(
    mqttService: MQTTService | null,
    projectId: string,
    raceName: string
): void {
    if (!mqttService) {
        console.warn('[MQTT工具] MQTT服务未初始化，无法发送项目切换消息');
        return;
    }

    try {
        const raceId = parseInt(projectId, 10);

        mqttService.publish(
            MQTTUtils.createTopic(
                MQTTDomain.SYSTEM,
                MQTTContext.RACE,
                MQTTTarget.ALL,
                MQTTAction.LOAD
            ),
            {
                raceId,
                raceName,
                status: "active",
                timestamp: Date.now(),
            }
        );

        console.log(`[MQTT工具] → 已发送: 赛事切换 - ${raceName}`, {
            topic: `${MQTTDomain.SYSTEM}/${MQTTContext.RACE}/${MQTTTarget.ALL}/${MQTTAction.LOAD}`,
            raceId,
            raceName,
            _type: "outbound"
        });
    } catch (error) {
        console.error("[MQTT工具] 发送赛事消息失败", {
            error: error instanceof Error ? error.message : String(error),
            projectId,
            raceName
        });
    }
}

/**
 * 发送导航切换消息
 * 
 * @param mqttService MQTT服务实例
 * @param navigationKey 导航键
 */
export function sendNavigationChangeMessage(
    mqttService: MQTTService | null,
    navigationKey: string | null
): void {
    if (!mqttService || !navigationKey) {
        return;
    }

    try {
        // 解析导航键，确定消息类型
        const sessionType = parseNavigationKey(navigationKey);

        if (!sessionType) {
            console.log(`[MQTT工具] 导航键 ${navigationKey} 无对应MQTT消息映射`);
            return;
        }

        const session = NAVIGATION_MQTT_MAPPING.sessions[sessionType as keyof typeof NAVIGATION_MQTT_MAPPING.sessions];

        if (session) {
            if (sessionType === "ranking") {
                // 排行榜显示
                mqttService.publish(
                    MQTTUtils.createTopic(
                        MQTTDomain.DISPLAY,
                        MQTTContext.RANK,
                        MQTTTarget.SCREEN,
                        MQTTAction.SHOW
                    ),
                    { rankType: "general", timestamp: Date.now() }
                );

                console.log("→ 已发送: 排行榜显示", {
                    topic: `${MQTTDomain.DISPLAY}/${MQTTContext.RANK}/${MQTTTarget.SCREEN}/${MQTTAction.SHOW}`,
                    rankType: "general",
                    _type: "outbound"
                });
            } else {
                // 环节开始
                mqttService.publish(
                    MQTTUtils.createTopic(
                        MQTTDomain.QUIZ,
                        MQTTContext.SESSION,
                        MQTTTarget.ALL,
                        MQTTAction.START
                    ),
                    {
                        sessionType,
                        sessionId: session.id,
                        sessionName: session.name,
                        startTime: Date.now(),
                        config: {},
                    }
                );

                console.log(`→ 已发送: 环节开始 - ${session.name}`, {
                    topic: `${MQTTDomain.QUIZ}/${MQTTContext.SESSION}/${MQTTTarget.ALL}/${MQTTAction.START}`,
                    sessionType,
                    sessionId: session.id,
                    sessionName: session.name,
                    _type: "outbound"
                });
            }
        }
    } catch (error) {
        console.error("[MQTT工具] 发送导航消息失败", {
            error: error instanceof Error ? error.message : String(error),
            navigationKey
        });
    }
}

/**
 * 解析导航键，确定对应的会话类型
 * 
 * @param navigationKey 导航键
 * @returns 会话类型或null
 */
function parseNavigationKey(navigationKey: string): string | null {
    // 根据导航键模式匹配对应的会话类型
    if (navigationKey.includes('qa') || navigationKey.includes('有问必答')) {
        return 'qa';
    }
    if (navigationKey.includes('onestation') || navigationKey.includes('一站到底')) {
        return 'onestation';
    }
    if (navigationKey.includes('timerace') || navigationKey.includes('争分夺秒')) {
        return 'timerace';
    }
    if (navigationKey.includes('finalpk') || navigationKey.includes('终极PK')) {
        return 'finalpk';
    }
    if (navigationKey.includes('tiebreak') || navigationKey.includes('同分加赛')) {
        return 'tiebreak';
    }
    if (navigationKey.includes('ranking') || navigationKey.includes('排名')) {
        return 'ranking';
    }

    return null;
}

/**
 * 获取项目对应的赛事名称
 * 
 * @param projectId 项目ID
 * @returns 赛事名称
 */
export function getRaceNameByProjectId(projectId: string): string {
    const index = parseInt(projectId, 10);
    return PROJECT_RACE_MAPPING[index] || `项目${projectId}`;
}
