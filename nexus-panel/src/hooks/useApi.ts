/**
 * 通用 API 请求状态管理 Hook (重构版)
 *
 * 重构为使用新的统一API架构，保持向后兼容性
 * 内部委托给统一API Hook系统实现
 */

import React from 'react';
import { useUnifiedApi } from './api/useUnifiedApi';
import type { UseApiOptions, UseApiReturn, ApiError } from '../services/api/types';
import { ApiStatus } from '../services/api/types';
import type { UseUnifiedApiConfig } from './api/types';

/**
 * 通用 API Hook (重构版)
 *
 * 使用新的统一API架构，保持向后兼容性
 * 内部委托给统一API Hook系统实现
 *
 * @param apiFunction - 要执行的 API 函数
 * @param options - Hook 配置选项
 * @returns API 状态和控制方法
 */
export function useApi<T = unknown>(
	apiFunction: () => Promise<T>,
	options: UseApiOptions<T> = {}
): UseApiReturn<T> {
	const {
		immediate = false,
		onSuccess,
		onError,
		onStart,
		onFinally,
	} = options;

	// 转换为新系统的配置格式 - 使用useMemo避免重复创建
	const unifiedConfig: UseUnifiedApiConfig<T> = React.useMemo(() => ({
		immediate,
		onSuccess,
		onError,
		onStart,
		onFinally,
		// 使用默认的重试配置
		retry: {
			maxRetries: 1,
			retryDelay: 1000
		}
	}), [immediate, onSuccess, onError, onStart, onFinally]);

	// 委托给统一API Hook
	const result = useUnifiedApi(apiFunction, unifiedConfig);

	// 转换状态格式以保持兼容性
	const compatibleStatus = result.status === 'idle' ? ApiStatus.IDLE :
		result.status === 'loading' ? ApiStatus.LOADING :
			result.status === 'success' ? ApiStatus.SUCCESS :
				ApiStatus.ERROR;

	// 返回兼容的接口
	return {
		data: result.data,
		loading: result.loading,
		error: result.error,
		status: compatibleStatus,
		execute: result.execute,
		retry: result.retry,
		reset: result.reset,
		refetch: result.refresh // refetch 等同于 refresh（清除缓存后重新执行）
	};
}

// ==================== 便捷 Hook 变体 ====================

/**
 * 自动执行的 API Hook
 * 组件挂载时自动执行 API 请求
 */
export function useApiImmediate<T = unknown>(
	apiFunction: () => Promise<T>,
	options: Omit<UseApiOptions<T>, 'immediate'> = {}
): UseApiReturn<T> {
	return useApi(apiFunction, { ...options, immediate: true });
}

/**
 * 手动执行的 API Hook
 * 需要手动调用 execute 方法执行请求
 */
export function useApiManual<T = unknown>(
	apiFunction: () => Promise<T>,
	options: Omit<UseApiOptions<T>, 'immediate'> = {}
): UseApiReturn<T> {
	return useApi(apiFunction, { ...options, immediate: false });
}

/**
 * 带重试功能的 API Hook (重构版)
 * 使用新的统一API系统的重试功能
 */
export function useApiWithRetry<T = unknown>(
	apiFunction: () => Promise<T>,
	maxRetries: number = 3,
	retryDelay: number = 1000,
	options: UseApiOptions<T> = {}
): UseApiReturn<T> {
	// 使用新系统的重试配置
	const enhancedOptions: UseApiOptions<T> = {
		...options,
		// 注入重试配置到新系统
	};

	// 创建带重试配置的统一API配置
	const unifiedConfig: UseUnifiedApiConfig<T> = {
		immediate: enhancedOptions.immediate,
		onSuccess: enhancedOptions.onSuccess,
		onError: enhancedOptions.onError,
		onStart: enhancedOptions.onStart,
		onFinally: enhancedOptions.onFinally,
		retry: {
			maxRetries,
			retryDelay,
			exponentialBackoff: true
		}
	};

	// 委托给统一API Hook
	const result = useUnifiedApi(apiFunction, unifiedConfig);

	// 转换状态格式以保持兼容性
	const compatibleStatus = result.status === 'idle' ? ApiStatus.IDLE :
		result.status === 'loading' ? ApiStatus.LOADING :
			result.status === 'success' ? ApiStatus.SUCCESS :
				ApiStatus.ERROR;

	return {
		data: result.data,
		loading: result.loading,
		error: result.error,
		status: compatibleStatus,
		execute: result.execute,
		retry: result.retry,
		reset: result.reset
	};
}
