/**
 * API Hook系统导出
 * 提供简化的API Hook生态系统
 */

// ==================== 新的简化API导出 ====================

// 核心简化Hook导出
export {
    useSimpleApi,
    useSimpleApiImmediate,
    useSimpleApiManual,
    useSimpleApiWithErrorHandler,
    useSimpleApiWithLogging
} from './useSimpleApi';

// 兼容性适配器导出
export {
    useApi,
    useApiImmediate,
    useApiManual,
    useApiWithRetry,
    useRaceApi,
    useRaceApiImmediate,
    useRaceApiManual,
    useRaceApiWithHealthCheck
} from './compatibilityAdapter';

// ==================== 旧的复杂API导出（已废弃） ====================

// @deprecated 建议使用新的简化API
export { useUnifiedApi, createApiHook, useApiPolling, useApiWithHealthCheck, useApiCached } from './useUnifiedApi';

// @deprecated 配置系统已简化
export { API_PRESETS, getApiPreset, mergeApiConfig, createCustomPreset, validateApiConfig, getRecommendedConfig } from './configs/apiConfigs';

// @deprecated 插件系统已移除
export { createRetryPlugin, executeWithRetry } from './plugins/retryPlugin';
export { createCachePlugin, cacheManager } from './plugins/cachePlugin';

// 类型定义导出
export * from './types';

// ==================== 默认导出 ====================

// 默认导出 - 推荐使用新的简化Hook
export { useSimpleApi as default } from './useSimpleApi';
