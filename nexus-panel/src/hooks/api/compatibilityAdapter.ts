/**
 * 兼容性适配器
 * 
 * 为现有组件提供兼容的API接口，确保无需修改现有代码
 * 内部使用新的简化API实现，保持外部接口完全兼容
 */

import { useSimpleApi, useSimpleApiImmediate, useSimpleApiManual } from './useSimpleApi';
import { 
  getRaces, 
  getVisibleRaces, 
  getNavigationData, 
  getConfigurationData,
  getRulesIntroductionData,
  getQuestions,
  getRankingData,
  checkRaceServiceHealth,
  checkNavigationServiceHealth,
  checkQuestionServiceHealth
} from '../../api/businessApi';

// 导入原有的类型定义以保持兼容性
import type {
  UseApiOptions as OriginalUseApiOptions,
  UseApiReturn as OriginalUseApiReturn,
  UseRaceApiOptions,
  UseRaceApiReturn,
  ProcessedRaceItem,
  ApiError,
  NavigationNode,
  GroupedConfigurationData,
  ProcessedRulesIntroductionItem,
  ProcessedQuestionItem,
  RankingData,
  ApiStatus
} from '../../services/api/types';

// ==================== useApi兼容适配器 ====================

/**
 * 兼容的useApi Hook
 * 
 * @deprecated 建议使用新的useSimpleApi
 * 保持与原有useApi完全相同的接口
 */
export function useApi<T = unknown>(
  apiFunction: () => Promise<T>,
  options: OriginalUseApiOptions<T> = {}
): OriginalUseApiReturn<T> {
  const {
    immediate = false,
    onSuccess,
    onError,
    onStart,
    onFinally,
  } = options;

  // 使用新的useSimpleApi实现
  const result = useSimpleApi(apiFunction, {
    immediate,
    onSuccess,
    onError,
    onStart,
    onFinally
  });

  // 转换状态格式以保持兼容性
  const status: ApiStatus = result.loading ? 'LOADING' : 
                           result.error ? 'ERROR' : 
                           result.data ? 'SUCCESS' : 'IDLE';

  return {
    data: result.data,
    loading: result.loading,
    error: result.error,
    status,
    execute: result.execute,
    retry: result.refetch,
    reset: result.reset,
    refetch: result.refetch
  };
}

/**
 * 兼容的useApiImmediate Hook
 * @deprecated 建议使用useSimpleApiImmediate
 */
export function useApiImmediate<T = unknown>(
  apiFunction: () => Promise<T>,
  options: Omit<OriginalUseApiOptions<T>, 'immediate'> = {}
): OriginalUseApiReturn<T> {
  return useApi(apiFunction, { ...options, immediate: true });
}

/**
 * 兼容的useApiManual Hook
 * @deprecated 建议使用useSimpleApiManual
 */
export function useApiManual<T = unknown>(
  apiFunction: () => Promise<T>,
  options: Omit<OriginalUseApiOptions<T>, 'immediate'> = {}
): OriginalUseApiReturn<T> {
  return useApi(apiFunction, { ...options, immediate: false });
}

/**
 * 兼容的useApiWithRetry Hook
 * @deprecated 新的简化API不支持复杂的重试逻辑，建议使用基础的useApi
 */
export function useApiWithRetry<T = unknown>(
  apiFunction: () => Promise<T>,
  maxRetries: number = 3,
  retryDelay: number = 1000,
  options: OriginalUseApiOptions<T> = {}
): OriginalUseApiReturn<T> {
  // 简化实现：不支持复杂的重试逻辑，仅提供基础功能
  console.warn('[兼容性警告] useApiWithRetry的重试功能已简化，建议迁移到新的API');
  return useApi(apiFunction, options);
}

// ==================== useRaceApi兼容适配器 ====================

/**
 * 兼容的useRaceApi Hook
 * 
 * @deprecated 建议逐步迁移到新的简化API
 * 保持与原有useRaceApi的核心接口兼容
 */
export function useRaceApi(options: UseRaceApiOptions = {}): UseRaceApiReturn {
  const {
    immediate = false,
    onError,
    onLog,
    selectedProject,
    selectedNavigationKey
  } = options;

  // 赛事数据
  const raceResult = useSimpleApi(
    () => getVisibleRaces().then(races => races.map(race => ({
      id: race['赛事 ID'],
      name: race['赛事名称'],
      visible: race['是否显示'] === '显示',
      dbId: race.Id,
      peopleCount: race['参赛人数'] || 17
    }))),
    { 
      immediate,
      onError: (error) => {
        onError?.(error);
        onLog?.('error', '获取赛事数据失败', { error: error.message });
      },
      onSuccess: (data) => {
        onLog?.('success', '获取赛事数据成功', { count: data.length });
      }
    }
  );

  // 导航数据
  const navigationResult = useSimpleApi(
    () => selectedProject ? getNavigationData(selectedProject) : Promise.resolve(null),
    {
      immediate: immediate && !!selectedProject,
      onError: (error) => {
        onLog?.('error', '获取导航数据失败', { error: error.message });
      }
    }
  );

  // 配置数据
  const configurationResult = useSimpleApi(
    () => selectedProject ? getConfigurationData(selectedProject) : Promise.resolve(null),
    {
      immediate: immediate && !!selectedProject,
      onError: (error) => {
        onLog?.('error', '获取配置数据失败', { error: error.message });
      }
    }
  );

  // 题目数据
  const questionResult = useSimpleApi(
    () => selectedProject ? getQuestions(selectedProject) : Promise.resolve(null),
    {
      immediate: immediate && !!selectedProject,
      onError: (error) => {
        onLog?.('error', '获取题目数据失败', { error: error.message });
      }
    }
  );

  // 排名数据
  const rankingResult = useSimpleApi(
    () => selectedProject ? getRankingData(selectedProject) : Promise.resolve(null),
    {
      immediate: immediate && !!selectedProject,
      onError: (error) => {
        onLog?.('error', '获取排名数据失败', { error: error.message });
      }
    }
  );

  // 健康检查
  const healthCheckResult = useSimpleApi(
    () => checkRaceServiceHealth(),
    { immediate: false }
  );

  // 构建兼容的返回值
  const races: ProcessedRaceItem[] = raceResult.data || [];
  const hasData = races.length > 0;
  const loading = raceResult.loading;
  const error = raceResult.error;

  return {
    // 基础赛事数据
    races,
    loading,
    error,
    hasData,
    count: races.length,
    refetch: raceResult.refetch,
    reset: raceResult.reset,
    
    // 查找方法
    findRaceById: (id: string) => races.find(race => race.id === id) || null,
    
    // 健康检查
    checkHealth: healthCheckResult.execute,
    
    // 导航相关功能
    navigationData: navigationResult.data as NavigationNode[] | null,
    navigationLoading: navigationResult.loading,
    navigationError: navigationResult.error,
    fetchNavigationData: navigationResult.execute,
    resetNavigationData: navigationResult.reset,
    
    // 配置信息相关功能
    configurationData: configurationResult.data as GroupedConfigurationData | null,
    configurationLoading: configurationResult.loading,
    configurationError: configurationResult.error,
    fetchConfigurationData: configurationResult.execute,
    resetConfigurationData: configurationResult.reset,
    
    // 规则介绍相关功能（简化实现）
    rulesIntroductionData: null,
    rulesIntroductionLoading: false,
    rulesIntroductionError: null,
    fetchRulesIntroductionData: async () => {},
    resetRulesIntroductionData: () => {},
    findRulesIntroductionByTitle: () => null,
    
    // 题目相关功能
    questionData: questionResult.data as ProcessedQuestionItem[] | null,
    questionLoading: questionResult.loading,
    questionError: questionResult.error,
    currentStage: '',
    currentPackage: '',
    fetchQuestionData: questionResult.execute,
    fetchQuestionDataWithStage: async () => {},
    resetQuestionData: questionResult.reset,
    findQuestionByNumber: () => null,
    setCurrentStage: () => {},
    setCurrentPackage: () => {},
    
    // 排名相关功能
    rankingData: rankingResult.data as RankingData | null,
    rankingLoading: rankingResult.loading,
    rankingError: rankingResult.error,
    rankingProgress: null,
    fetchRankingData: rankingResult.execute,
    resetRankingData: rankingResult.reset,
    
    // 合并数据功能（简化实现）
    allDataReady: hasData && !!navigationResult.data,
    fetchAllProjectData: async () => {
      await Promise.all([
        navigationResult.execute(),
        configurationResult.execute(),
        questionResult.execute(),
        rankingResult.execute()
      ]);
    },
    resetAllProjectData: () => {
      navigationResult.reset();
      configurationResult.reset();
      questionResult.reset();
      rankingResult.reset();
    },
    
    // 缓存相关功能（简化实现）
    cachePerformanceStats: { hits: 0, misses: 0, totalRequests: 0, lastReportTime: Date.now() },
    clearTableStructureCache: async () => {},
    
    // 选手列表数据相关功能（简化实现）
    playerListData: null,
    playerListLoading: false,
    playerListError: null,
    fetchPlayerListData: async () => {},
    resetPlayerListData: () => {},
    
    // 事件处理器（简化实现）
    handlers: {
      handlePackageChange: () => {},
      handlePackageChangeConfirm: () => {},
      handlePackageChangeCancel: () => {},
      handleStageChange: () => {}
    }
  };
}

/**
 * 兼容的useRaceApiImmediate Hook
 * @deprecated 建议迁移到新的简化API
 */
export function useRaceApiImmediate(
  options: Omit<UseRaceApiOptions, 'immediate'> = {}
): UseRaceApiReturn {
  return useRaceApi({ ...options, immediate: true });
}

/**
 * 兼容的useRaceApiManual Hook
 * @deprecated 建议迁移到新的简化API
 */
export function useRaceApiManual(
  options: Omit<UseRaceApiOptions, 'immediate'> = {}
): UseRaceApiReturn {
  return useRaceApi({ ...options, immediate: false });
}

/**
 * 兼容的useRaceApiWithHealthCheck Hook
 * @deprecated 建议迁移到新的简化API
 */
export function useRaceApiWithHealthCheck(
  options: UseRaceApiOptions = {},
  healthCheckInterval: number = 30000
): UseRaceApiReturn & { serviceHealthy: boolean | null } {
  const raceApi = useRaceApi(options);
  
  // 简化的健康检查实现
  const healthResult = useSimpleApi(
    () => checkRaceServiceHealth(),
    { immediate: true }
  );
  
  return {
    ...raceApi,
    serviceHealthy: healthResult.data
  };
}
