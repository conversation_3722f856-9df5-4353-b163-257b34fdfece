/**
 * API配置预设定义
 * 提供常用的API调用配置模板
 */

import type { UseUnifiedApiConfig, ApiPresetConfig } from '../types';

/**
 * API预设配置
 * 整合了原useApi和useRaceApi中的重复变体函数配置
 */
export const API_PRESETS: ApiPresetConfig = {
  /** 立即执行配置 - 替代useApiImmediate和useRaceApiImmediate */
  immediate: {
    immediate: true,
    retry: { 
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true
    },
    cache: { 
      enabled: true, 
      ttl: 300000 // 5分钟
    }
  },

  /** 手动执行配置 - 替代useApiManual和useRaceApiManual */
  manual: {
    immediate: false,
    retry: { 
      maxRetries: 1,
      retryDelay: 500
    },
    cache: { 
      enabled: false
    }
  },

  /** 轮询配置 - 用于实时数据更新 */
  polling: {
    immediate: true,
    polling: { 
      enabled: true, 
      interval: 5000,
      stopOnError: false,
      maxPolls: 100
    },
    cache: { 
      enabled: false // 轮询时不使用缓存
    },
    retry: {
      maxRetries: 2,
      retryDelay: 1000
    }
  },

  /** 健康检查配置 - 替代useRaceApiWithHealthCheck */
  healthCheck: {
    immediate: true,
    healthCheck: true,
    retry: { 
      maxRetries: 5, 
      retryDelay: 2000,
      exponentialBackoff: true,
      shouldRetry: (error) => {
        // 网络错误或服务器错误时重试
        return error.message.includes('network') || 
               error.message.includes('timeout') ||
               error.message.includes('500');
      }
    },
    cache: { 
      enabled: true, 
      ttl: 60000 // 1分钟短缓存
    }
  },

  /** 缓存优先配置 - 用于静态数据 */
  cached: {
    immediate: true,
    cache: { 
      enabled: true, 
      ttl: 1800000, // 30分钟长缓存
      useMemoryCache: true
    },
    retry: {
      maxRetries: 2,
      retryDelay: 500
    }
  }
} as const;

/**
 * 获取预设配置
 */
export function getApiPreset(type: keyof ApiPresetConfig): UseUnifiedApiConfig<any> {
  return { ...API_PRESETS[type] };
}

/**
 * 合并配置
 * 将用户配置与预设配置合并
 */
export function mergeApiConfig<T>(
  preset: keyof ApiPresetConfig,
  userConfig: Partial<UseUnifiedApiConfig<T>> = {}
): UseUnifiedApiConfig<T> {
  const presetConfig = getApiPreset(preset);
  
  return {
    ...presetConfig,
    ...userConfig,
    // 深度合并嵌套对象
    retry: {
      ...presetConfig.retry,
      ...userConfig.retry
    },
    cache: {
      ...presetConfig.cache,
      ...userConfig.cache
    },
    polling: {
      ...presetConfig.polling,
      ...userConfig.polling
    }
  };
}

/**
 * 创建自定义预设
 */
export function createCustomPreset<T>(
  name: string,
  config: UseUnifiedApiConfig<T>
): UseUnifiedApiConfig<T> {
  // 可以在这里添加自定义预设的验证逻辑
  return { ...config };
}

/**
 * 验证配置
 */
export function validateApiConfig<T>(config: UseUnifiedApiConfig<T>): boolean {
  // 基本验证
  if (config.retry && config.retry.maxRetries !== undefined && config.retry.maxRetries < 0) {
    console.warn('[API Config] maxRetries不能为负数');
    return false;
  }

  if (config.cache && config.cache.ttl !== undefined && config.cache.ttl < 0) {
    console.warn('[API Config] cache TTL不能为负数');
    return false;
  }

  if (config.polling && config.polling.interval !== undefined && config.polling.interval < 1000) {
    console.warn('[API Config] 轮询间隔不应小于1秒');
    return false;
  }

  return true;
}

/**
 * 获取推荐配置
 * 根据使用场景推荐最佳配置
 */
export function getRecommendedConfig(scenario: 'realtime' | 'static' | 'user-action' | 'background'): keyof ApiPresetConfig {
  switch (scenario) {
    case 'realtime':
      return 'polling';
    case 'static':
      return 'cached';
    case 'user-action':
      return 'immediate';
    case 'background':
      return 'manual';
    default:
      return 'immediate';
  }
}
