/**
 * 统一API Hook类型定义
 * 整合所有API相关的类型和接口
 */

/**
 * API Hook配置选项
 */
export interface UseUnifiedApiConfig<T> {
  /** 是否立即执行 */
  immediate?: boolean;
  /** 重试配置 */
  retry?: RetryConfig;
  /** 缓存配置 */
  cache?: CacheConfig;
  /** 轮询配置 */
  polling?: PollingConfig;
  /** 健康检查配置 */
  healthCheck?: boolean;
  /** 数据转换函数 */
  transform?: (data: any) => T;
  /** 成功回调 */
  onSuccess?: (data: T) => void;
  /** 错误回调 */
  onError?: (error: ApiError) => void;
  /** 开始回调 */
  onStart?: () => void;
  /** 完成回调 */
  onFinally?: () => void;
}

/**
 * 重试配置
 */
export interface RetryConfig {
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟 (毫秒) */
  retryDelay?: number;
  /** 是否使用指数退避 */
  exponentialBackoff?: boolean;
  /** 重试条件判断函数 */
  shouldRetry?: (error: Error) => boolean;
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 是否启用缓存 */
  enabled?: boolean;
  /** 缓存生存时间 (毫秒) */
  ttl?: number;
  /** 缓存键 */
  key?: string;
  /** 是否使用内存缓存 */
  useMemoryCache?: boolean;
}

/**
 * 轮询配置
 */
export interface PollingConfig {
  /** 是否启用轮询 */
  enabled?: boolean;
  /** 轮询间隔 (毫秒) */
  interval?: number;
  /** 错误时是否停止轮询 */
  stopOnError?: boolean;
  /** 最大轮询次数 */
  maxPolls?: number;
}

/**
 * API Hook返回值
 */
export interface UseUnifiedApiReturn<T> {
  /** 数据 */
  data: T | null;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: ApiError | null;
  /** 执行状态 */
  status: ApiStatus;
  /** 手动执行函数 */
  execute: () => Promise<void>;
  /** 重试函数 */
  retry: () => Promise<void>;
  /** 重置函数 */
  reset: () => void;
  /** 刷新函数 */
  refresh: () => Promise<void>;
}

/**
 * API状态枚举
 */
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * API错误类型
 */
export interface ApiError {
  /** 错误消息 */
  message: string;
  /** 错误代码 */
  code?: string | number;
  /** 错误类型 */
  type?: string;
  /** 原始错误 */
  originalError?: Error;
  /** 错误上下文 */
  context?: Record<string, any>;
}

/**
 * API插件接口
 */
export interface ApiPlugin {
  /** 插件名称 */
  name: string;
  /** 插件执行顺序 */
  order: number;
  /** 请求前处理 */
  beforeRequest?: (config: UseUnifiedApiConfig<any>) => UseUnifiedApiConfig<any>;
  /** 请求后处理 */
  afterRequest?: (data: any, config: UseUnifiedApiConfig<any>) => any;
  /** 错误处理 */
  onError?: (error: ApiError, config: UseUnifiedApiConfig<any>) => ApiError;
}

/**
 * API预设类型
 */
export type ApiPresetType = 'immediate' | 'manual' | 'polling' | 'healthCheck' | 'cached';

/**
 * API预设配置映射
 */
export interface ApiPresetConfig {
  immediate: UseUnifiedApiConfig<any>;
  manual: UseUnifiedApiConfig<any>;
  polling: UseUnifiedApiConfig<any>;
  healthCheck: UseUnifiedApiConfig<any>;
  cached: UseUnifiedApiConfig<any>;
}

/**
 * Hook工厂函数类型
 */
export type HookFactory<T> = (config?: UseUnifiedApiConfig<T>) => UseUnifiedApiReturn<T>;

/**
 * API函数类型
 */
export type ApiFunction<T> = () => Promise<T>;

/**
 * 插件管理器接口
 */
export interface PluginManager {
  /** 注册插件 */
  register: (plugin: ApiPlugin) => void;
  /** 移除插件 */
  unregister: (name: string) => void;
  /** 获取所有插件 */
  getPlugins: () => ApiPlugin[];
  /** 执行插件链 */
  executePlugins: (phase: 'beforeRequest' | 'afterRequest' | 'onError', ...args: any[]) => any;
}
