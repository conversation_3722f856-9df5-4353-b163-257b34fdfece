/**
 * 简化的useApi Hook
 * 
 * 替代复杂的useUnifiedApi系统，提供轻量级的React状态管理
 * 保留必要的功能：状态管理、生命周期处理、错误处理
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import type { ApiError } from '../../utils/api/simpleFetch';

// ==================== 类型定义 ====================

/**
 * useSimpleApi Hook选项
 */
export interface UseSimpleApiOptions {
  /** 是否立即执行API调用 */
  immediate?: boolean;
  /** 成功回调 */
  onSuccess?: (data: any) => void;
  /** 错误回调 */
  onError?: (error: ApiError) => void;
  /** 开始执行回调 */
  onStart?: () => void;
  /** 完成回调（无论成功失败） */
  onFinally?: () => void;
}

/**
 * useSimpleApi Hook返回值
 */
export interface UseSimpleApiReturn<T> {
  /** 响应数据 */
  data: T | null;
  /** 加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: ApiError | null;
  /** 执行API调用 */
  execute: () => Promise<void>;
  /** 重新获取数据（等同于execute） */
  refetch: () => Promise<void>;
  /** 重置状态 */
  reset: () => void;
}

// ==================== 核心Hook ====================

/**
 * 简化的API Hook
 * 
 * 提供基础的API状态管理功能，替代复杂的useUnifiedApi
 * 
 * @param apiCall API调用函数
 * @param options Hook选项
 * @returns Hook返回值
 */
export const useSimpleApi = <T>(
  apiCall: () => Promise<T>,
  options: UseSimpleApiOptions = {}
): UseSimpleApiReturn<T> => {
  // 状态管理
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  
  // 组件挂载状态引用，防止组件卸载后设置状态
  const mountedRef = useRef(true);
  
  // 选项引用，避免不必要的重新渲染
  const optionsRef = useRef(options);
  optionsRef.current = options;

  // 执行API调用的核心函数
  const execute = useCallback(async () => {
    if (!mountedRef.current) return;
    
    // 开始执行
    setLoading(true);
    setError(null);
    optionsRef.current.onStart?.();
    
    try {
      // 执行API调用
      const result = await apiCall();
      
      // 只有在组件仍然挂载时才更新状态
      if (mountedRef.current) {
        setData(result);
        optionsRef.current.onSuccess?.(result);
      }
    } catch (err) {
      // 只有在组件仍然挂载时才更新状态
      if (mountedRef.current) {
        const apiError = err as ApiError;
        setError(apiError);
        optionsRef.current.onError?.(apiError);
      }
    } finally {
      // 只有在组件仍然挂载时才更新状态
      if (mountedRef.current) {
        setLoading(false);
        optionsRef.current.onFinally?.();
      }
    }
  }, [apiCall]);

  // 重置状态函数
  const reset = useCallback(() => {
    if (!mountedRef.current) return;
    
    setData(null);
    setLoading(false);
    setError(null);
  }, []);

  // 处理immediate选项
  useEffect(() => {
    if (options.immediate) {
      execute();
    }
  }, [options.immediate, execute]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    refetch: execute, // refetch等同于execute
    reset
  };
};

// ==================== 便捷Hook变体 ====================

/**
 * 立即执行的API Hook
 * 组件挂载时自动执行API调用
 */
export const useSimpleApiImmediate = <T>(
  apiCall: () => Promise<T>,
  options: Omit<UseSimpleApiOptions, 'immediate'> = {}
): UseSimpleApiReturn<T> => {
  return useSimpleApi(apiCall, { ...options, immediate: true });
};

/**
 * 手动执行的API Hook
 * 需要手动调用execute方法执行请求
 */
export const useSimpleApiManual = <T>(
  apiCall: () => Promise<T>,
  options: Omit<UseSimpleApiOptions, 'immediate'> = {}
): UseSimpleApiReturn<T> => {
  return useSimpleApi(apiCall, { ...options, immediate: false });
};

// ==================== 工具函数 ====================

/**
 * 创建带有默认错误处理的API Hook
 * @param apiCall API调用函数
 * @param defaultErrorHandler 默认错误处理函数
 * @param options 其他选项
 * @returns Hook返回值
 */
export const useSimpleApiWithErrorHandler = <T>(
  apiCall: () => Promise<T>,
  defaultErrorHandler: (error: ApiError) => void,
  options: UseSimpleApiOptions = {}
): UseSimpleApiReturn<T> => {
  const enhancedOptions: UseSimpleApiOptions = {
    ...options,
    onError: (error) => {
      // 先调用默认错误处理器
      defaultErrorHandler(error);
      // 再调用用户提供的错误处理器
      options.onError?.(error);
    }
  };

  return useSimpleApi(apiCall, enhancedOptions);
};

/**
 * 创建带有日志记录的API Hook
 * @param apiCall API调用函数
 * @param logFunction 日志记录函数
 * @param options 其他选项
 * @returns Hook返回值
 */
export const useSimpleApiWithLogging = <T>(
  apiCall: () => Promise<T>,
  logFunction: (level: 'info' | 'error', message: string, details?: unknown) => void,
  options: UseSimpleApiOptions = {}
): UseSimpleApiReturn<T> => {
  const enhancedOptions: UseSimpleApiOptions = {
    ...options,
    onStart: () => {
      logFunction('info', 'API调用开始');
      options.onStart?.();
    },
    onSuccess: (data) => {
      logFunction('info', 'API调用成功', { dataType: typeof data });
      options.onSuccess?.(data);
    },
    onError: (error) => {
      logFunction('error', 'API调用失败', { 
        message: error.message, 
        code: error.code,
        status: error.status 
      });
      options.onError?.(error);
    }
  };

  return useSimpleApi(apiCall, enhancedOptions);
};

// ==================== 默认导出 ====================

export default useSimpleApi;
