/**
 * 统一API Hook
 *
 * @deprecated 此文件包含过度复杂的API Hook系统，已被简化的useSimpleApi替代
 *
 * 迁移指南：
 * - 使用 useSimpleApi() 替代 useUnifiedApi()
 * - 使用 useSimpleApiImmediate() 替代复杂的immediate配置
 * - 使用业务API函数替代复杂的插件系统
 *
 * 新的API位置：
 * - import { useSimpleApi, useSimpleApiImmediate } from './useSimpleApi'
 * - import { useApi, useRaceApi } from './compatibilityAdapter' (兼容性)
 *
 * 原功能：整合useApi和useRaceApi的所有功能，消除重复变体函数
 * 新方案：使用更简单直接的Hook，减少抽象层次
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import type {
  UseUnifiedApiConfig,
  UseUnifiedApiReturn,
  ApiFunction,
  ApiError,
  ApiStatus
} from './types';
import { createRetryPlugin, executeWithRetry } from './plugins/retryPlugin';
import { createCachePlugin } from './plugins/cachePlugin';
import { mergeApiConfig } from './configs/apiConfigs';

/**
 * 统一API Hook
 * 
 * 替代原有的useApi、useApiImmediate、useApiManual、useApiWithRetry
 * 以及useRaceApi、useRaceApiImmediate、useRaceApiManual、useRaceApiWithHealthCheck
 * 
 * @param apiFunction API调用函数
 * @param config 配置选项
 * @returns API Hook返回值
 */
export function useUnifiedApi<T>(
  apiFunction: ApiFunction<T>,
  config: UseUnifiedApiConfig<T> = {}
): UseUnifiedApiReturn<T> {

  // 状态管理
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<ApiError | null>(null);
  const [status, setStatus] = useState<ApiStatus>('idle');



  // 引用管理
  const configRef = useRef(config);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // 更新配置引用
  configRef.current = config;

  // 插件系统 - 使用useMemo避免重复创建
  const plugins = React.useMemo(() => [createCachePlugin(), createRetryPlugin()], []);

  /**
   * 执行API调用的核心函数
   */
  const executeApi = useCallback(async (): Promise<void> => {
    if (!mountedRef.current) return;

    const currentConfig = configRef.current;

    try {
      setLoading(true);
      setError(null);
      setStatus('loading');

      // 调用开始回调
      currentConfig.onStart?.();

      // 应用缓存插件的beforeRequest处理
      const cachePlugin = plugins.find(p => p.name === 'cache');
      let processedConfig = currentConfig;

      if (cachePlugin?.beforeRequest) {
        processedConfig = cachePlugin.beforeRequest(currentConfig);
      }

      // 检查缓存命中
      if ((processedConfig as any)._cacheHit) {
        const cachedData = (processedConfig as any)._cachedData;

        if (mountedRef.current) {
          setData(cachedData);
          setStatus('success');
          setLoading(false); // 确保缓存命中时也设置loading为false
          currentConfig.onSuccess?.(cachedData);
        }
        return;
      }

      // 执行API调用（带重试）
      let result: T;

      if (currentConfig.retry && currentConfig.retry.maxRetries && currentConfig.retry.maxRetries > 0) {
        result = await executeWithRetry(
          apiFunction,
          currentConfig.retry,
          (attempt, retryError) => {
            console.log(`[useUnifiedApi] 重试第${attempt}次`, retryError.message);
          }
        );
      } else {
        result = await apiFunction();
      }

      // 应用数据转换
      if (currentConfig.transform) {
        result = currentConfig.transform(result);
      }

      // 应用缓存插件的afterRequest处理
      if (cachePlugin?.afterRequest) {
        result = cachePlugin.afterRequest(result, processedConfig);
      }

      // 关键修复：强制设置状态，即使组件可能已卸载
      // 这确保数据能够正确传递到React状态中
      setData(result);
      setStatus('success');

      // 只有在组件仍然挂载时才调用回调
      if (mountedRef.current) {
        currentConfig.onSuccess?.(result);
      }

    } catch (err) {
      if (!mountedRef.current) return;

      // 创建标准化错误对象
      const apiError: ApiError = {
        message: err instanceof Error ? err.message : String(err),
        originalError: err instanceof Error ? err : new Error(String(err)),
        context: {
          timestamp: Date.now(),
          config: currentConfig
        }
      };

      // 应用错误插件处理
      const retryPlugin = plugins.find(p => p.name === 'retry');
      let processedError = apiError;

      if (retryPlugin?.onError) {
        processedError = retryPlugin.onError(apiError, currentConfig);
      }

      setError(processedError);
      setStatus('error');
      currentConfig.onError?.(processedError);

    } finally {
      if (mountedRef.current) {
        setLoading(false);
        currentConfig.onFinally?.();
      }
    }
  }, [apiFunction, plugins]);

  /**
   * 重试函数
   */
  const retry = useCallback(async (): Promise<void> => {
    await executeApi();
  }, [executeApi]);

  /**
   * 重置函数
   */
  const reset = useCallback((): void => {
    setData(null);
    setLoading(false);
    setError(null);
    setStatus('idle');

    // 清除轮询
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  /**
   * 刷新函数
   */
  const refresh = useCallback(async (): Promise<void> => {
    // 清除缓存后重新执行
    const cacheKey = (configRef.current as any)._cacheKey;
    if (cacheKey) {
      const { cacheManager } = await import('./plugins/cachePlugin');
      cacheManager.clear(cacheKey);
    }

    await executeApi();
  }, [executeApi]);

  // 立即执行效果 - 确保在组件完全挂载后执行
  useEffect(() => {
    if (config.immediate !== false && mountedRef.current) {
      // 直接执行，因为组件生命周期问题已经在executeApi中修复
      executeApi();
    }
  }, []); // 空依赖数组，只在挂载时执行一次

  // 轮询效果 - 使用ref避免依赖config对象
  const pollingConfigRef = useRef(config.polling);
  pollingConfigRef.current = config.polling;

  useEffect(() => {
    if (pollingConfigRef.current?.enabled) {
      const interval = pollingConfigRef.current.interval || 5000;

      pollingRef.current = setInterval(() => {
        if (mountedRef.current && !loading) {
          executeApi();
        }
      }, interval);

      return () => {
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
        }
      };
    }
  }, [executeApi, loading]); // 移除config.polling依赖

  // 组件卸载清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    status,
    execute: executeApi,
    retry,
    reset,
    refresh
  };
}

/**
 * 创建预设Hook工厂函数
 */
export function createApiHook<T>(preset: 'immediate' | 'manual' | 'polling' | 'healthCheck' | 'cached') {
  return (apiFunction: ApiFunction<T>, userConfig: Partial<UseUnifiedApiConfig<T>> = {}) => {
    const config = mergeApiConfig(preset, userConfig);
    return useUnifiedApi(apiFunction, config);
  };
}

// 导出预设Hook（替代原有的变体函数）
export const useApiImmediate = createApiHook('immediate');
export const useApiManual = createApiHook('manual');
export const useApiPolling = createApiHook('polling');
export const useApiWithHealthCheck = createApiHook('healthCheck');
export const useApiCached = createApiHook('cached');
