/**
 * 重试插件
 * 提供智能重试功能
 */

import type { ApiPlugin, ApiError, UseUnifiedApiConfig } from '../types';

/**
 * 创建重试插件
 */
export function createRetryPlugin(): ApiPlugin {
  return {
    name: 'retry',
    order: 100,

    onError: (error: ApiError, config: UseUnifiedApiConfig<any>): ApiError => {
      const retryConfig = config.retry;
      
      if (!retryConfig || !retryConfig.maxRetries || retryConfig.maxRetries <= 0) {
        return error;
      }

      // 检查是否应该重试
      if (retryConfig.shouldRetry && !retryConfig.shouldRetry(error.originalError || new Error(error.message))) {
        return error;
      }

      // 添加重试信息到错误上下文
      const retryContext = {
        ...error.context,
        retryable: true,
        maxRetries: retryConfig.maxRetries,
        retryDelay: retryConfig.retryDelay || 1000,
        exponentialBackoff: retryConfig.exponentialBackoff || false
      };

      return {
        ...error,
        context: retryContext
      };
    }
  };
}

/**
 * 重试执行器
 * 实际执行重试逻辑
 */
export async function executeWithRetry<T>(
  apiFunction: () => Promise<T>,
  retryConfig: NonNullable<UseUnifiedApiConfig<T>['retry']>,
  onRetry?: (attempt: number, error: Error) => void
): Promise<T> {
  const { maxRetries = 3, retryDelay = 1000, exponentialBackoff = false } = retryConfig;
  
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiFunction();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 检查是否应该重试
      if (retryConfig.shouldRetry && !retryConfig.shouldRetry(lastError)) {
        break;
      }

      // 调用重试回调
      onRetry?.(attempt + 1, lastError);

      // 计算延迟时间
      const delay = exponentialBackoff 
        ? retryDelay * Math.pow(2, attempt)
        : retryDelay;

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
