/**
 * 缓存插件
 * 提供智能缓存功能
 */

import type { ApiPlugin, UseUnifiedApiConfig } from '../types';

/**
 * 内存缓存存储
 */
class MemoryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 全局缓存实例
const globalCache = new MemoryCache();

/**
 * 创建缓存插件
 */
export function createCachePlugin(): ApiPlugin {
  return {
    name: 'cache',
    order: 10,

    beforeRequest: (config: UseUnifiedApiConfig<any>): UseUnifiedApiConfig<any> => {
      const cacheConfig = config.cache;
      
      if (!cacheConfig || !cacheConfig.enabled) {
        return config;
      }

      // 生成缓存键
      const cacheKey = cacheConfig.key || generateCacheKey(config);
      
      // 尝试从缓存获取数据
      const cachedData = globalCache.get(cacheKey);
      
      if (cachedData !== null) {
        // 返回带有缓存数据的配置
        return {
          ...config,
          _cachedData: cachedData,
          _cacheKey: cacheKey,
          _cacheHit: true
        };
      }

      // 缓存未命中，添加缓存键到配置
      return {
        ...config,
        _cacheKey: cacheKey,
        _cacheHit: false
      };
    },

    afterRequest: (data: any, config: UseUnifiedApiConfig<any>): any => {
      const cacheConfig = config.cache;
      
      if (!cacheConfig || !cacheConfig.enabled || (config as any)._cacheHit) {
        return data;
      }

      // 存储到缓存
      const cacheKey = (config as any)._cacheKey;
      const ttl = cacheConfig.ttl || 300000; // 默认5分钟
      
      if (cacheKey) {
        globalCache.set(cacheKey, data, ttl);
      }

      return data;
    }
  };
}

/**
 * 生成缓存键
 */
function generateCacheKey(config: UseUnifiedApiConfig<any>): string {
  // 基于配置生成唯一的缓存键
  const keyParts = [
    'api',
    config.transform?.toString() || 'default',
    JSON.stringify(config.cache),
    Date.now().toString(36) // 添加时间戳确保唯一性
  ];
  
  return keyParts.join(':');
}

/**
 * 缓存管理器
 */
export const cacheManager = {
  /**
   * 清除所有缓存
   */
  clearAll(): void {
    globalCache.clear();
  },

  /**
   * 清除指定键的缓存
   */
  clear(key: string): void {
    globalCache.delete(key);
  },

  /**
   * 获取缓存统计
   */
  getStats(): { size: number; keys: string[] } {
    return globalCache.getStats();
  },

  /**
   * 检查缓存是否存在
   */
  has(key: string): boolean {
    return globalCache.has(key);
  },

  /**
   * 手动设置缓存
   */
  set(key: string, data: any, ttl: number = 300000): void {
    globalCache.set(key, data, ttl);
  },

  /**
   * 手动获取缓存
   */
  get(key: string): any | null {
    return globalCache.get(key);
  }
};
