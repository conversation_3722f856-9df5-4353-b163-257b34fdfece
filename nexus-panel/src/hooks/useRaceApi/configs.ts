/**
 * useRaceApi专用配置
 * 基于统一API系统的赛事API配置
 */

import type { UseUnifiedApiConfig } from '../api/types';
import { mergeApiConfig } from '../api/configs/apiConfigs';

/**
 * 赛事API专用预设配置
 */
export const RACE_API_PRESETS = {
  /** 标准赛事数据获取 */
  standard: {
    immediate: true,
    retry: {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true
    },
    cache: {
      enabled: true,
      ttl: 300000 // 5分钟缓存
    }
  },

  /** 实时赛事数据 */
  realtime: {
    immediate: true,
    polling: {
      enabled: true,
      interval: 5000,
      stopOnError: false
    },
    cache: {
      enabled: false // 实时数据不缓存
    },
    retry: {
      maxRetries: 2,
      retryDelay: 1000
    }
  },

  /** 健康检查配置 */
  healthCheck: {
    immediate: true,
    healthCheck: true,
    retry: {
      maxRetries: 5,
      retryDelay: 2000,
      exponentialBackoff: true
    },
    cache: {
      enabled: true,
      ttl: 60000 // 1分钟短缓存
    }
  },

  /** 导航数据配置 */
  navigation: {
    immediate: true,
    cache: {
      enabled: true,
      ttl: 1800000 // 30分钟长缓存
    },
    retry: {
      maxRetries: 2,
      retryDelay: 500
    }
  }
} as const;

/**
 * 获取赛事API预设配置
 */
export function getRaceApiPreset(type: keyof typeof RACE_API_PRESETS): UseUnifiedApiConfig<any> {
  return { ...RACE_API_PRESETS[type] };
}

/**
 * 创建赛事API配置
 */
export function createRaceApiConfig<T>(
  preset: keyof typeof RACE_API_PRESETS,
  userConfig: Partial<UseUnifiedApiConfig<T>> = {}
): UseUnifiedApiConfig<T> {
  return mergeApiConfig(preset as any, {
    ...getRaceApiPreset(preset),
    ...userConfig
  });
}
