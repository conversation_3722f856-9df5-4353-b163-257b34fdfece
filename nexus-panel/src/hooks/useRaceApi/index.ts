/**
 * useRaceApi Hook 模块入口文件
 */

// 导出主要的 Hook
export { useRaceApi, useRaceApiImmediate, useRaceApiManual, useRaceApiWithHealthCheck } from './useRaceApi';

// 导出类型定义
export type {
    UseRaceApiOptions,
    UseRaceApiReturn,
    RaceApiHandlers,
    ProcessedRaceItem,
    ApiError,
    NavigationNode,
    GroupedConfigurationData,
    ProcessedRulesIntroductionItem,
    ProcessedQuestionItem,
    RankingData,
    RankingProgress,
    TableStructureResponse,
    CacheStats,
    CachePerformanceStats,
    PlayerInfoApiItem
} from './types';

// 导出简化的工具函数
export { getTableStructureWithCache, clearProjectCache, getCacheStats } from './utils';