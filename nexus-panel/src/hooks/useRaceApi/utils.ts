/**
 * useRaceApi Hook 工具函数模块
 * 
 * 简化的工具函数，替代复杂的cache.ts
 */

import type { TableStructureResponse } from './types';

/**
 * 简化的缓存工具函数
 * 直接使用全局缓存，避免复杂的状态管理
 */
export async function getTableStructureWithCache(baseId: string): Promise<TableStructureResponse> {
    const { GlobalTableStructureCache } = await import('../../services/api/tableStructureCache');
    
    return GlobalTableStructureCache.getWithCache(baseId, async (id) => {
        const { getTableStructure } = await import('../../services/api');
        const response = await getTableStructure(id);
        return response.data;
    });
}

/**
 * 清理指定项目的缓存
 */
export async function clearProjectCache(baseId: string): Promise<void> {
    const { GlobalTableStructureCache } = await import('../../services/api/tableStructureCache');
    GlobalTableStructureCache.clear(baseId);
}

/**
 * 获取缓存统计信息
 */
export async function getCacheStats(): Promise<{ hits: number; misses: number; size: number }> {
    const { GlobalTableStructureCache } = await import('../../services/api/tableStructureCache');
    return GlobalTableStructureCache.getStats();
}
