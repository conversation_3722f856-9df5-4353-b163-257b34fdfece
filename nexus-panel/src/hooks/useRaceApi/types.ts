/**
 * useRaceApi Hook 相关类型定义
 */

import type { ProcessedRaceItem, ApiError, NavigationNode, GroupedConfigurationData, ProcessedRulesIntroductionItem, ProcessedQuestionItem, RankingData, RankingProgress, TableStructureResponse, CacheStats, CachePerformanceStats, PlayerInfoApiItem } from '../../services/api/types';



/**
 * 赛事 API Hook 配置选项
 */
export interface UseRaceApiOptions {
    /** 是否在组件挂载时自动获取数据 */
    immediate?: boolean;
    /** 数据获取成功回调 */
    onSuccess?: (races: ProcessedRaceItem[]) => void;
    /** 数据获取失败回调 */
    onError?: (error: ApiError) => void;
    /** 日志记录回调函数（简化版） */
    onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void;
    /** 当前选中的项目ID（用于事件处理器） */
    selectedProject?: string | null;
    /** 当前选中的导航键（用于事件处理器） */
    selectedNavigationKey?: string | null;

    /** 包切换对话框状态管理 */
    packageChangeDialog?: {
        isOpen: boolean;
        packageId: string;
        packageName: string;
    };
    /** 设置包切换对话框状态 */
    setPackageChangeDialog?: (dialog: { isOpen: boolean; packageId: string; packageName: string }) => void;
}

/**
 * 赛事API事件处理器接口
 */
export interface RaceApiHandlers {
    /** 处理包切换 */
    handlePackageChange: (packageId: string, packageName: string) => void;
    /** 处理包切换确认 */
    handlePackageChangeConfirm: () => void;
    /** 处理包切换取消 */
    handlePackageChangeCancel: () => void;
    /** 处理阶段切换 */
    handleStageChange: (stageName: string) => void;
}

/**
 * 赛事 API Hook 返回值
 */
export interface UseRaceApiReturn {
    /** 赛事数据列表 */
    races: ProcessedRaceItem[];
    /** 加载状态 */
    loading: boolean;
    /** 错误信息 */
    error: ApiError | null;
    /** 是否有数据 */
    hasData: boolean;
    /** 数据数量 */
    count: number;
    /** 手动刷新数据 */
    refetch: () => Promise<void>;
    /** 重置状态 */
    reset: () => void;
    /** 根据 ID 查找赛事 */
    findRaceById: (id: string) => ProcessedRaceItem | undefined;
    /** 检查服务健康状态 */
    checkHealth: () => Promise<boolean>;

    // 新增导航相关功能
    /** 导航数据列表 */
    navigationData: NavigationNode[] | null;
    /** 导航数据加载状态 */
    navigationLoading: boolean;
    /** 导航数据错误信息 */
    navigationError: ApiError | null;
    /** 获取导航数据 */
    fetchNavigationData: (baseId: string) => Promise<void>;
    /** 重置导航数据 */
    resetNavigationData: () => void;

    // 新增配置信息相关功能
    /** 配置数据 */
    configurationData: GroupedConfigurationData | null;
    /** 配置数据加载状态 */
    configurationLoading: boolean;
    /** 配置数据错误信息 */
    configurationError: ApiError | null;
    /** 获取配置数据 */
    fetchConfigurationData: (baseId: string) => Promise<void>;
    /** 重置配置数据 */
    resetConfigurationData: () => void;

    // 新增规则介绍相关功能
    /** 规则介绍数据 */
    rulesIntroductionData: ProcessedRulesIntroductionItem[] | null;
    /** 规则介绍数据加载状态 */
    rulesIntroductionLoading: boolean;
    /** 规则介绍数据错误信息 */
    rulesIntroductionError: ApiError | null;
    /** 获取规则介绍数据 */
    fetchRulesIntroductionData: (baseId: string) => Promise<void>;
    /** 重置规则介绍数据 */
    resetRulesIntroductionData: () => void;
    /** 根据标题查找规则介绍项 */
    findRulesIntroductionByTitle: (title: string) => ProcessedRulesIntroductionItem | undefined;

    // 新增题目相关功能
    /** 题目数据列表 */
    questionData: ProcessedQuestionItem[] | null;
    /** 题目数据加载状态 */
    questionLoading: boolean;
    /** 题目数据错误信息 */
    questionError: ApiError | null;
    /** 当前阶段 */
    currentStage: string | null;
    /** 当前题包 */
    currentPackage: string | null;
    /** 获取题目数据 */
    fetchQuestionData: (baseId: string, sectionName: string, packageNumber?: string, stage?: string) => Promise<void>;
    /** 获取指定阶段的题目数据 */
    fetchQuestionDataWithStage: (baseId: string, sectionName: string, stage: string, packageNumber?: string) => Promise<void>;
    /** 重置题目数据 */
    resetQuestionData: () => void;
    /** 根据题号查找题目 */
    findQuestionByNumber: (questionNumber: number) => ProcessedQuestionItem | undefined;
    /** 设置当前阶段 */
    setCurrentStage: (stage: string | null) => void;
    /** 设置当前题包 */
    setCurrentPackage: (packageId: string | null) => void;

    // 新增合并数据功能
    /** 所有数据是否就绪 */
    allDataReady: boolean;
    /** 获取所有项目数据（导航+配置） */
    fetchAllProjectData: (baseId: string) => Promise<void>;
    /** 重置所有项目数据 */
    resetAllProjectData: () => void;

    // 表结构缓存相关功能
    /** 表结构缓存性能统计 */
    cachePerformanceStats: CachePerformanceStats;
    /** 清理表结构缓存 */
    clearTableStructureCache: (baseId?: string) => void;
    /** 当前项目的表结构数据 */
    currentTableStructure: TableStructureResponse | null;

    // 排名数据相关功能
    /** 排名数据 */
    rankingData: RankingData | null;
    /** 排名数据加载状态 */
    rankingLoading: boolean;
    /** 排名数据错误信息 */
    rankingError: ApiError | null;
    /** 排名数据获取进度 */
    rankingProgress: RankingProgress | null;
    /** 获取排名数据 */
    fetchRankingData: (baseId: string) => Promise<void>;
    /** 重置排名数据 */
    resetRankingData: () => void;

    // 环节排名数据相关功能
    /** 环节排名数据 */
    sectionRankingData: RankingData | null;
    /** 环节排名数据加载状态 */
    sectionRankingLoading: boolean;
    /** 环节排名数据错误信息 */
    sectionRankingError: ApiError | null;
    /** 获取环节排名数据 */
    fetchSectionRankingData: (baseId: string, sectionName: string) => Promise<void>;
    /** 开始环节排名数据轮询 */
    startSectionRankingPolling: (baseId: string, sectionName: string, interval?: number) => void;
    /** 停止环节排名数据轮询 */
    stopSectionRankingPolling: () => void;
    /** 重置环节排名数据 */
    resetSectionRankingData: (sectionName?: string) => void;
    /** 切换到指定环节 */
    switchToSection: (sectionName: string) => void;
    /** 当前环节名称 */
    currentSectionName: string | null;

    // 选手列表数据相关功能
    /** 选手列表数据 */
    playerListData: PlayerInfoApiItem[] | null;
    /** 选手列表数据加载状态 */
    playerListLoading: boolean;
    /** 选手列表数据错误信息 */
    playerListError: ApiError | null;
    /** 获取选手列表数据 */
    fetchPlayerListData: (baseId: string) => Promise<void>;
    /** 重置选手列表数据 */
    resetPlayerListData: () => void;

    // 事件处理器（包含验证和日志记录逻辑）
    /** 事件处理器 */
    handlers: RaceApiHandlers;
}

// 重新导出API类型，方便使用
export type {
    ProcessedRaceItem,
    ApiError,
    NavigationNode,
    GroupedConfigurationData,
    ProcessedRulesIntroductionItem,
    ProcessedQuestionItem,
    RankingData,
    RankingProgress,
    TableStructureResponse,
    CacheStats,
    CachePerformanceStats,
    PlayerInfoApiItem
};