/**
 * 统一API配置
 * 
 * 替代原本分散在4个服务文件中的重复配置
 * 消除API Token和端点的重复定义
 */

export interface ApiConfig {
  /** API基础URL */
  baseUrl: string;
  /** 认证Token */
  token: string;
  /** 请求超时时间（毫秒） */
  timeout: number;
  /** API端点配置 */
  endpoints: {
    /** 赛事表端点 */
    races: string;
    /** 赛事视图ID */
    raceView: string;
    /** 导航数据端点模板 */
    navigation: string;
    /** 题目数据端点模板 */
    questions: string;
    /** 排名数据端点模板 */
    ranking: string;
  };
  /** 默认请求参数 */
  defaults: {
    /** 默认分页大小 */
    pageSize: number;
    /** 默认请求限制 */
    limit: number;
  };
}

/**
 * 统一的API配置
 * 整合了原本分散在各个API服务中的重复配置
 */
export const API_CONFIG: ApiConfig = {
  // 基础配置
  baseUrl: process.env.VITE_API_BASE_URL || 'https://noco.ohvfx.com/api/v2',
  token: process.env.VITE_API_TOKEN || 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
  timeout: 10000,
  
  // 端点配置
  endpoints: {
    // 赛事相关端点（原本在raceApi.ts中重复定义）
    races: '/tables/m19dww1xfzsfipk/records',
    raceView: 'vwoimmnq6pws8pso',
    
    // 动态端点模板（{baseId}将被实际的表ID替换）
    navigation: '/tables/{baseId}/records',
    questions: '/tables/{baseId}/records',
    ranking: '/tables/{baseId}/records'
  },
  
  // 默认参数
  defaults: {
    pageSize: 8,
    limit: 500
  }
};

/**
 * 构建完整的API端点URL
 * @param endpoint 端点路径或模板
 * @param baseId 可选的表ID，用于替换模板中的{baseId}
 * @returns 完整的API URL
 */
export const buildApiUrl = (endpoint: string, baseId?: string): string => {
  let url = endpoint;
  
  // 如果包含模板变量，进行替换
  if (baseId && url.includes('{baseId}')) {
    url = url.replace('{baseId}', baseId);
  }
  
  // 如果不是完整URL，添加基础URL
  if (!url.startsWith('http')) {
    url = `${API_CONFIG.baseUrl}${url}`;
  }
  
  return url;
};

/**
 * 获取标准请求头
 * @param customHeaders 自定义请求头
 * @returns 合并后的请求头
 */
export const getApiHeaders = (customHeaders?: Record<string, string>): Record<string, string> => {
  return {
    'xc-token': API_CONFIG.token,
    'Content-Type': 'application/json',
    ...customHeaders
  };
};

/**
 * 构建查询参数字符串
 * @param params 查询参数对象
 * @returns URL查询字符串
 */
export const buildQueryString = (params: Record<string, string | number | boolean>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    searchParams.append(key, String(value));
  });
  
  return searchParams.toString();
};

/**
 * 验证API配置的有效性
 * @returns 配置是否有效
 */
export const validateApiConfig = (): boolean => {
  const { baseUrl, token, endpoints } = API_CONFIG;
  
  // 检查必要的配置项
  if (!baseUrl || !token) {
    console.error('[API Config] 缺少必要的配置项：baseUrl 或 token');
    return false;
  }
  
  // 检查端点配置
  if (!endpoints.races || !endpoints.raceView) {
    console.error('[API Config] 缺少必要的端点配置');
    return false;
  }
  
  return true;
};

// 在模块加载时验证配置
if (process.env.NODE_ENV === 'development') {
  validateApiConfig();
}
