/**
 * 统一UI配置管理
 * 整合所有UI相关的配置和常量
 */

/**
 * UI配置接口定义
 */
interface UiConfig {
  skeleton: {
    animationDuration: number;
    defaultHeight: string;
    backgroundColor: string;
    borderRadius: string;
  };
  polling: {
    defaultInterval: number;
    maxInterval: number;
    minInterval: number;
  };
  device: {
    defaultCount: number;
    maxCount: number;
  };
  animation: {
    fadeInDuration: number;
    fadeOutDuration: number;
    transitionTiming: string;
  };
}

/**
 * 统一的UI配置
 * 整合了原本分散在各个组件中的重复配置
 */
export const UI_CONFIG: UiConfig = {
  skeleton: {
    // 骨架屏动画持续时间 (原本在多个骨架屏组件中重复)
    animationDuration: 2000,
    // 默认高度
    defaultHeight: 'auto',
    // 背景颜色 (原本在6个骨架屏组件中重复)
    backgroundColor: 'gray-100',
    // 边框圆角 (原本在6个骨架屏组件中重复)
    borderRadius: 'medium'
  },
  polling: {
    // 默认轮询间隔 (原本在多个地方重复)
    defaultInterval: 5000,
    // 最大轮询间隔
    maxInterval: 30000,
    // 最小轮询间隔
    minInterval: 1000
  },
  device: {
    // 默认设备数量 (原本在设备管理中重复)
    defaultCount: 17,
    // 最大设备数量
    maxCount: 50
  },
  animation: {
    // 淡入动画持续时间
    fadeInDuration: 300,
    // 淡出动画持续时间
    fadeOutDuration: 200,
    // 过渡时间函数
    transitionTiming: 'ease-in-out'
  }
};

/**
 * 获取骨架屏配置
 */
export function getSkeletonConfig() {
  return UI_CONFIG.skeleton;
}

/**
 * 获取轮询配置
 */
export function getPollingConfig() {
  return UI_CONFIG.polling;
}

/**
 * 获取设备配置
 */
export function getDeviceConfig() {
  return UI_CONFIG.device;
}

/**
 * 获取动画配置
 */
export function getAnimationConfig() {
  return UI_CONFIG.animation;
}

/**
 * UI配置的默认导出
 */
export default UI_CONFIG;
