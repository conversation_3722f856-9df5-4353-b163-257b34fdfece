/**
 * API相关常量定义
 * 整合所有API服务中的重复常量
 */

/**
 * HTTP状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * API错误类型常量
 */
export const API_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

/**
 * API请求方法常量
 */
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const;

/**
 * 缓存键前缀常量
 */
export const CACHE_KEYS = {
  TABLE_STRUCTURE: 'table_structure_',
  RACE_DATA: 'race_data_',
  RANKING_DATA: 'ranking_data_',
  QUESTION_DATA: 'question_data_',
  NAVIGATION_DATA: 'navigation_data_',
} as const;

/**
 * API端点路径常量
 */
export const API_PATHS = {
  META_BASES: '/meta/bases',
  TABLES: '/tables',
  RECORDS: '/records',
  VIEWS: '/views',
} as const;

/**
 * 请求配置常量
 */
export const REQUEST_CONFIG = {
  DEFAULT_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  DEFAULT_LIMIT: 500,
  MAX_LIMIT: 1000,
} as const;

/**
 * 内容类型常量
 */
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
} as const;

/**
 * API版本常量
 */
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
  CURRENT: 'v2',
} as const;
