/**
 * UI相关常量定义
 * 整合所有UI组件中的重复常量
 */

/**
 * 尺寸常量 (Adobe React Spectrum)
 */
export const SIZES = {
  SIZE_50: 'size-50',
  SIZE_100: 'size-100',
  SIZE_150: 'size-150',
  SIZE_200: 'size-200',
  SIZE_250: 'size-250',
  SIZE_300: 'size-300',
  SIZE_350: 'size-350',
  SIZE_400: 'size-400',
  SIZE_500: 'size-500',
  SIZE_600: 'size-600',
  SIZE_800: 'size-800',
  SIZE_1000: 'size-1000',
  SIZE_1200: 'size-1200',
  SIZE_3000: 'size-3000',
} as const;

/**
 * 颜色常量 (Adobe React Spectrum)
 */
export const COLORS = {
  GRAY_100: 'gray-100',
  GRAY_200: 'gray-200',
  GRAY_300: 'gray-300',
  GRAY_400: 'gray-400',
  GRAY_500: 'gray-500',
  BLUE_400: 'blue-400',
  GREEN_400: 'green-400',
  RED_400: 'red-400',
  YELLOW_400: 'yellow-400',
} as const;

/**
 * 边框圆角常量
 */
export const BORDER_RADIUS = {
  NONE: 'none',
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large',
  FULL: 'full',
} as const;

/**
 * 动画持续时间常量 (毫秒)
 */
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  SKELETON: 2000,
} as const;

/**
 * 动画缓动函数常量
 */
export const ANIMATION_EASING = {
  EASE: 'ease',
  EASE_IN: 'ease-in',
  EASE_OUT: 'ease-out',
  EASE_IN_OUT: 'ease-in-out',
  LINEAR: 'linear',
} as const;

/**
 * 布局方向常量
 */
export const FLEX_DIRECTION = {
  ROW: 'row',
  COLUMN: 'column',
  ROW_REVERSE: 'row-reverse',
  COLUMN_REVERSE: 'column-reverse',
} as const;

/**
 * 对齐方式常量
 */
export const JUSTIFY_CONTENT = {
  FLEX_START: 'flex-start',
  CENTER: 'center',
  FLEX_END: 'flex-end',
  SPACE_BETWEEN: 'space-between',
  SPACE_AROUND: 'space-around',
  SPACE_EVENLY: 'space-evenly',
} as const;

/**
 * 设备状态常量
 */
export const DEVICE_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  CONNECTING: 'connecting',
  ERROR: 'error',
} as const;

/**
 * 加载状态常量
 */
export const LOADING_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

/**
 * 主题常量
 */
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto',
} as const;
