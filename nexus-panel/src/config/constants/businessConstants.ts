/**
 * 业务相关常量定义
 * 整合所有业务逻辑中的重复常量
 */

/**
 * 竞赛状态常量
 */
export const RACE_STATUS = {
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  PAUSED: 'paused',
  FINISHED: 'finished',
  CANCELLED: 'cancelled',
} as const;

/**
 * 问题类型常量
 */
export const QUESTION_TYPES = {
  SINGLE_CHOICE: 'single_choice',
  MULTIPLE_CHOICE: 'multiple_choice',
  TRUE_FALSE: 'true_false',
  FILL_BLANK: 'fill_blank',
  ESSAY: 'essay',
} as const;

/**
 * 排名类型常量
 */
export const RANKING_TYPES = {
  SCORE: 'score',
  TIME: 'time',
  ACCURACY: 'accuracy',
  COMPREHENSIVE: 'comprehensive',
} as const;

/**
 * 内容类型常量
 */
export const CONTENT_TYPES = {
  HOME: 'home',
  QUESTION: 'question',
  RANKING: 'ranking',
  RULES: 'rules',
  SAFETY: 'safety',
  ULTIMATE_PK: 'ultimate_pk',
} as const;

/**
 * 导航节点类型常量
 */
export const NAVIGATION_TYPES = {
  FOLDER: 'folder',
  ITEM: 'item',
  SEPARATOR: 'separator',
} as const;

/**
 * 按钮组类型常量
 */
export const BUTTON_GROUP_TYPES = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  NAVIGATION: 'navigation',
  ACTION: 'action',
} as const;

/**
 * 音频状态常量
 */
export const AUDIO_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  PLAYING: 'playing',
  PAUSED: 'paused',
  ENDED: 'ended',
  ERROR: 'error',
} as const;

/**
 * MQTT连接状态常量
 */
export const MQTT_STATUS = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error',
} as const;

/**
 * 时间赛阶段常量
 */
export const TIME_RACE_PHASES = {
  PREPARATION: 'preparation',
  COUNTDOWN: 'countdown',
  RACING: 'racing',
  FINISHED: 'finished',
} as const;

/**
 * PK模式常量
 */
export const PK_MODES = {
  STANDARD: 'standard',
  ULTIMATE: 'ultimate',
  TEAM: 'team',
  INDIVIDUAL: 'individual',
} as const;

/**
 * 数据更新频率常量 (毫秒)
 */
export const UPDATE_INTERVALS = {
  REAL_TIME: 1000,
  FREQUENT: 5000,
  NORMAL: 10000,
  SLOW: 30000,
} as const;
