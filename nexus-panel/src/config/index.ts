/**
 * 配置系统统一导出
 * 提供完整的配置管理生态系统
 */

// 原有配置模块导出 (保持向后兼容)
export * from "./buttonGroupConfigurations";
export * from "./componentConfigurations";
export * from "./navigationConfigurations";

// 新增全局配置导出
export { API_CONFIG, getApiHeaders, getApiEndpoint, getTableStructureUrl, getRaceApiUrl } from './global/apiConfig';
export { UI_CONFIG, getSkeletonConfig, getPollingConfig, getDeviceConfig, getAnimationConfig } from './global/uiConfig';

// 常量定义导出
export * from './constants';

// 默认导出 - 最常用的配置
export { API_CONFIG as default } from './global/apiConfig';
