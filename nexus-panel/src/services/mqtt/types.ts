/**
 * MQTT服务类型定义
 *
 * 定义了新MQTT架构的所有类型接口和枚举
 */

// MQTT连接配置接口
export interface MQTTConfig {
	/** MQTT代理服务器URL */
	brokerUrl: string;
	/** 客户端唯一标识 */
	clientId: string;
	/** 用户名（可选） */
	username?: string;
	/** 密码（可选） */
	password?: string;
	/** 心跳间隔（秒），默认60秒 */
	keepalive?: number;
	/** 重连间隔（毫秒），默认1000毫秒 */
	reconnectPeriod?: number;
	/** 连接超时（毫秒），默认30000毫秒 */
	connectTimeout?: number;
}

// MQTT消息数据类型联合
export type MQTTMessageData =
	| SessionData
	| RankData
	| PlayerData
	| RuleData
	| PrizeData
	| RaceData
	| QuestionData
	| SystemRefreshData
	| Record<string, unknown>; // 兜底类型，用于未定义的数据结构

// 标准MQTT消息结构
export interface MQTTMessage {
	/** 消息时间戳 */
	timestamp: number;
	/** 发送方标识 */
	sender: string;
	/** 消息版本，用于兼容性处理 */
	version?: string;
	/** 具体业务数据 */
	data: MQTTMessageData;
}

// Topic结构定义：{domain}/{context}/{target}/{action}
export interface MQTTTopicStructure {
	/** 业务域 */
	domain: MQTTDomain;
	/** 具体场景 */
	context: MQTTContext;
	/** 作用目标 */
	target: string;
	/** 行为动作 */
	action: MQTTAction;
}

// 业务域类型定义
export type MQTTDomain = "quiz" | "display" | "system";

// 业务域常量
export const MQTTDomain = {
	/** 环节控制 */
	QUIZ: "quiz" as const,
	/** 界面显示 */
	DISPLAY: "display" as const,
	/** 系统控制 */
	SYSTEM: "system" as const,
} as const;

// 具体场景类型定义
export type MQTTContext =
	| "session"
	| "rank"
	| "player"
	| "rule"
	| "prize"
	| "client"
	| "navigation"
	| "race"
	| "question"
	| "answer";

// 具体场景常量
export const MQTTContext = {
	/** 环节本身 */
	SESSION: "session" as const,
	/** 排行榜 */
	RANK: "rank" as const,
	/** 选手 */
	PLAYER: "player" as const,
	/** 规则 */
	RULE: "rule" as const,
	/** 奖项 */
	PRIZE: "prize" as const,
	/** 客户端 */
	CLIENT: "client" as const,
	/** 导航 */
	NAVIGATION: "navigation" as const,
	/** 赛事 */
	RACE: "race" as const,
	/** 题目 */
	QUESTION: "question" as const,
	/** 答案 */
	ANSWER: "answer" as const,
} as const;

// 行为动作类型定义
export type MQTTAction =
	| "start"
	| "show"
	| "hide"
	| "update"
	| "refresh"
	| "home"
	| "load"
	| "switch"
	| "submit"
	| "pause"
	| "stop"
	| "reset";

// 内存管理相关类型定义
export interface MQTTMemoryConfig {
	/** 定期清理间隔（毫秒），默认30分钟 */
	cleanupInterval?: number;
	/** 消息处理器数量阈值，默认50 */
	handlerThreshold?: number;

	/** 处理器过期时间（毫秒），默认1小时 */
	handlerExpireTime?: number;
	/** 是否启用调试日志，默认false */
	debug?: boolean;
}

export interface MQTTMemoryStats {
	/** 当前消息处理器数量 */
	handlerCount: number;
	/** 当前订阅数量 */
	subscriptionCount: number;

	/** 最后清理时间 */
	lastCleanupTime: Date | null;
	/** 总清理次数 */
	totalCleanups: number;
	/** 内存健康状态 */
	healthStatus: 'healthy' | 'warning' | 'critical';
}

export interface MQTTCleanupResult {
	/** 清理时间戳 */
	timestamp: Date;
	/** 清理的处理器数量 */
	handlersRemoved: number;
	/** 清理的订阅数量 */
	subscriptionsRemoved: number;
	/** 清理的日志数量 */
	logsRemoved: number;
	/** 清理耗时（毫秒） */
	duration: number;
	/** 清理原因 */
	reason: 'scheduled' | 'threshold' | 'manual';
}

// 行为动作常量
export const MQTTAction = {
	/** 开始 */
	START: "start" as const,
	/** 显示 */
	SHOW: "show" as const,
	/** 隐藏 */
	HIDE: "hide" as const,
	/** 更新 */
	UPDATE: "update" as const,
	/** 刷新 */
	REFRESH: "refresh" as const,
	/** 回到主页 */
	HOME: "home" as const,
	/** 加载 */
	LOAD: "load" as const,
	/** 切换 */
	SWITCH: "switch" as const,
	/** 提交 */
	SUBMIT: "submit" as const,
	/** 暂停 */
	PAUSE: "pause" as const,
	/** 停止 */
	STOP: "stop" as const,
	/** 重置 */
	RESET: "reset" as const,
} as const;

// 常用目标标识类型定义
export type MQTTTarget =
	| "all"
	| "screen"
	| "player-1"
	| "player-2"
	| "player-3"
	| "player-4";

// 常用目标标识常量
export const MQTTTarget = {
	/** 所有人 */
	ALL: "all" as const,
	/** 大屏 */
	SCREEN: "screen" as const,
	/** 1号选手 */
	PLAYER_1: "player-1" as const,
	/** 2号选手 */
	PLAYER_2: "player-2" as const,
	/** 3号选手 */
	PLAYER_3: "player-3" as const,
	/** 4号选手 */
	PLAYER_4: "player-4" as const,
} as const;

// 消息处理器类型
export type MQTTMessageHandler = (message: MQTTMessage, topic: string) => void;

// 连接状态类型定义
export type MQTTConnectionStatus =
	| "disconnected"
	| "connecting"
	| "connected"
	| "reconnecting"
	| "error";

// 连接状态常量
export const MQTTConnectionStatus = {
	/** 未连接 */
	DISCONNECTED: "disconnected" as const,
	/** 连接中 */
	CONNECTING: "connecting" as const,
	/** 已连接 */
	CONNECTED: "connected" as const,
	/** 重连中 */
	RECONNECTING: "reconnecting" as const,
	/** 连接错误 */
	ERROR: "error" as const,
} as const;

// MQTT服务事件类型
export interface MQTTServiceEvents {
	/** 连接状态变化 */
	statusChange: (status: MQTTConnectionStatus) => void;
	/** 连接错误 */
	error: (error: Error) => void;
	/** 消息接收 */
	message: (topic: string, message: MQTTMessage) => void;
	/** 订阅成功 */
	subscribed: (topic: string) => void;
	/** 取消订阅 */
	unsubscribed: (topic: string) => void;
}

// 环节类型定义
export interface SessionData {
	/** 环节类型 */
	sessionType: string;
	/** 环节ID */
	sessionId: number;
	/** 环节名称 */
	sessionName: string;
	/** 环节配置（可选） */
	config?: Record<string, unknown>;
}

// 排行榜数据定义
export interface RankData {
	/** 排行榜类型 */
	rankType: "general" | "speedrun" | "speedrun-plus";
	/** 排行榜数据 */
	rankings?: Array<{
		playerId: number;
		playerName: string;
		score: number;
		rank: number;
	}>;
}

// 选手数据定义
export interface PlayerData {
	/** 选手ID */
	playerId: number;
	/** 选手姓名 */
	playerName: string;
	/** 选手位置 */
	position: number;
	/** 选手状态（可选） */
	status?: "active" | "inactive" | "eliminated";
	/** 选手分数（可选） */
	score?: number;
}

// 规则数据定义
export interface RuleData {
	/** 规则类型 */
	ruleType: string;
	/** 规则ID */
	ruleId: number;
	/** 规则名称 */
	ruleName: string;
	/** 规则内容（可选） */
	content?: string;
}

// 奖项数据定义
export interface PrizeData {
	/** 奖项等级 */
	prizeLevel: "special" | "first" | "second" | "third" | "participation";
	/** 奖项ID */
	prizeId: number;
	/** 奖项名称 */
	prizeName: string;
	/** 奖项描述（可选） */
	description?: string;
}

// 赛事数据定义
export interface RaceData {
	/** 赛事ID（支持字符串和数字类型，对应API中的"赛事 ID"字段） */
	raceId: string | number;
	/** 赛事名称 */
	raceName: string;
	/** 赛事状态（可选） */
	status?: "pending" | "active" | "completed";
}

// 题目数据定义
export interface QuestionData {
	/** 题目编号 */
	questionNumber: number;
	/** 题目内容（可选） */
	content?: string;
	/** 题目类型（可选） */
	type?: "single" | "multiple" | "judge" | "fill";
}

// 系统刷新数据定义
export interface SystemRefreshData {
	/** 刷新类型 */
	refreshType: "full" | "partial" | "config" | "display";
	/** 时间戳 */
	timestamp: number;
	/** 刷新范围（可选） */
	scope?: string[];
}
