/**
 * API 服务模块统一导出
 *
 * 统一导出所有 API 相关的类型、服务和工具
 */

// ==================== 新的简化API导出 ====================

// 统一配置导出
export { API_CONFIG, buildApiUrl, getApiHeaders, buildQueryString, validateApiConfig } from '../../config/api/apiConfig';

// 简化fetch工具导出
export { simpleFetch, get, post, createApiError, isApiError, createHttpError } from '../../utils/api/simpleFetch';

// 业务API函数导出
export * from '../../api/businessApi';

// ==================== 旧的复杂服务类导出（已废弃） ====================

// @deprecated 建议使用新的简化API
export { HttpClient, httpClient, configureHttpClient } from './client';
// @deprecated 建议使用新的业务API函数
export { RaceApiService } from './raceApi';
// @deprecated 建议使用新的业务API函数
export { NavigationApiService } from './navigationApi';
// @deprecated 错误处理已简化
export { ApiErrorHandler } from './errors';

// ==================== 内部导入（用于工具函数） ====================

import { configureHttpClient } from './client';
import { checkRaceServiceHealth } from './raceApi';

// ==================== 类型定义导出 ====================

export type {
	// 基础 API 类型
	ApiConfig,
	ApiResponse,
	ApiError,
	RequestOptions,
	UseApiOptions,
	UseApiReturn,

	// 赛事相关类型
	RaceApiItem,
	RaceApiResponse,
	ProcessedRaceItem,

	// 导航相关类型
	TableStructureItem,
	TableStructureResponse,
	SectionDataItem,
	SectionDataResponse,
	NavigationNode,

	// 配置信息相关类型
	AttachmentItem,
	ConfigurationDataItem,
	ConfigurationDataResponse,
	ProcessedConfigurationItem,
	GroupedConfigurationData,

	// 规则介绍相关类型
	RulesIntroductionApiItem,
	RulesIntroductionApiResponse,
	ProcessedRulesIntroductionItem,

	// 排名相关类型
	AnswerRecordApiItem,
	AnswerRecordApiResponse,
	PlayerInfoApiItem,
	PlayerInfoApiResponse,
	PlayerScore,
	RankingData,
	RankingProgress,
} from './types';

export {
	// 枚举导出
	ApiStatus,
	HttpMethod,
} from './types';

// ==================== 错误处理导出 ====================

export {
	ApiErrorType,
	ERROR_MESSAGES,
	createNetworkError,
	createAuthError,
	createServerError,
	createDataFormatError,
} from './errors';

// ==================== 便捷函数导出 ====================

export {
	// HTTP 客户端便捷函数
	get,
	post,
	put,
	del as delete,
	patch,
} from './client';

export {
	// 赛事 API 便捷函数
	getVisibleRaces,
	getAllRaces,
	getRaceById,
	checkRaceServiceHealth,
} from './raceApi';

export {
	// 导航 API 便捷函数
	getTableStructure,
	getSectionData,
	getNavigationData,
	checkNavigationServiceHealth,
	// 配置信息 API 便捷函数
	getConfigurationData,
	getProcessedConfigurationData,
	getGroupedConfigurationData,
	// 规则介绍 API 便捷函数
	getSimpleRulesIntroductionData,
	getRulesIntroductionData,
	getProcessedRulesIntroductionData,
} from './navigationApi';

export {
	// 题目 API 便捷函数
	getQuestionDataRaw,
	getQuestionData,
	checkQuestionServiceHealth,
	// 题目数据处理函数
	parseOptionsString,
	extractAudioUrls,
} from './questionApi';

export {
	// 排名 API 便捷函数
	getRankingData,
	getAnswerRecords,
	getPlayerInfo,
	getSectionRankingData,
} from './rankingApi';

// ==================== 预设配置导出 ====================

/**
 * API 预设配置
 */
export const ApiPresets = {
	/**
	 * 开发环境配置
	 */
	development: {
		timeout: 15000, // 15秒超时
		defaultHeaders: {
			'Content-Type': 'application/json',
		},
	},

	/**
	 * 生产环境配置
	 */
	production: {
		timeout: 10000, // 10秒超时
		defaultHeaders: {
			'Content-Type': 'application/json',
		},
	},

	/**
	 * 测试环境配置
	 */
	testing: {
		timeout: 5000, // 5秒超时
		defaultHeaders: {
			'Content-Type': 'application/json',
		},
	},
} as const;

// ==================== 工具函数导出 ====================

/**
 * 初始化 API 服务
 * 根据环境自动配置 HTTP 客户端
 */
export function initializeApiService(environment: 'development' | 'production' | 'testing' = 'development'): void {
	const config = ApiPresets[environment];
	configureHttpClient(config);
}

/**
 * 检查 API 服务健康状态
 * 检查所有关键 API 服务的可用性
 */
export async function checkApiServiceHealth(): Promise<{
	race: boolean;
	overall: boolean;
}> {
	try {
		// 检查赛事服务
		const raceHealthy = await checkRaceServiceHealth();

		// 计算整体健康状态
		const overall = raceHealthy;

		return {
			race: raceHealthy,
			overall,
		};
	} catch (error) {
		console.error('API 服务健康检查失败:', error);
		return {
			race: false,
			overall: false,
		};
	}
}

/**
 * 获取 API 服务状态摘要
 */
export async function getApiServiceSummary(): Promise<{
	status: 'healthy' | 'degraded' | 'down';
	services: {
		race: 'up' | 'down';
	};
	timestamp: number;
}> {
	const health = await checkApiServiceHealth();

	let status: 'healthy' | 'degraded' | 'down';
	if (health.overall) {
		status = 'healthy';
	} else if (health.race) {
		status = 'degraded';
	} else {
		status = 'down';
	}

	return {
		status,
		services: {
			race: health.race ? 'up' : 'down',
		},
		timestamp: Date.now(),
	};
}
