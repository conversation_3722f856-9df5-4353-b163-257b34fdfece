/**
 * 安全公开课骨架屏组件 - 重构为使用新的分层组合架构
 */

import React from 'react';
import { SafetyPublicClassSkeleton as NewSafetyPublicClassSkeleton } from './common/skeleton/presets';
import type { SimpleSkeletonProps } from './common/skeleton/types';

/**
 * SafetyPublicClassSkeleton 组件的 Props 接口 - Next.js 风格优化
 */
export interface SafetyPublicClassSkeletonProps {
	/** 自定义 CSS 类名 */
	className?: string;
	/** 是否显示动画效果，默认为 true */
	animated?: boolean;
	/** 自定义高度，默认为 "auto" */
	height?: string;
	/** 是否启用交互效果，默认为 false */
	interactive?: boolean;
	/** 加载状态：'loading' | 'error' | 'success'，默认为 'loading' */
	status?: "loading" | "error" | "success";
	/** 淡入淡出效果：'fade-in' | 'fade-out' | null */
	fadeEffect?: "fade-in" | "fade-out" | null;
}

/**
 * SafetyPublicClassSkeleton - 安全公开课环节的骨架屏加载状态组件 (重构版)
 *
 * 使用新的分层组合架构，保持原有接口兼容性
 * 内部委托给新的骨架屏系统实现
 *
 * @param props - 组件属性
 * @returns JSX.Element
 */
export function SafetyPublicClassSkeleton({
	className,
	animated,
	height,
	interactive,
	status,
	fadeEffect,
}: SafetyPublicClassSkeletonProps): JSX.Element {

	// 转换为新系统的属性格式
	const newProps: SimpleSkeletonProps = {
		className,
		animated,
		height,
		interactive,
		status,
		fadeEffect,
		contentType: 'safety',
	};

	// 委托给新的骨架屏系统
	return <NewSafetyPublicClassSkeleton {...newProps} />;
}

export default SafetyPublicClassSkeleton;
