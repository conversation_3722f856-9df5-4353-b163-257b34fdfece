/**
 * 骨架屏标题区域组件
 * 用于渲染标题占位符
 */

import React from 'react';
import { View } from '@adobe/react-spectrum';

import type { SkeletonTitleProps } from '../types';
import { 
  SKELETON_SIZES, 
  SKELETON_SPACING, 
  SKELETON_STYLES 
} from '../constants';

/**
 * 骨架屏标题组件
 * 
 * 提供标题区域的骨架屏占位符，支持：
 * - 自定义宽度、高度和边距
 * - 不同的标题级别
 * - 统一的样式和动画
 * 
 * @param props 标题组件属性
 * @returns JSX.Element
 */
export function SkeletonTitle({
  width = '60%',
  height = SKELETON_SIZES.TITLE_HEIGHT,
  marginBottom = SKELETON_SPACING.TITLE_MARGIN_BOTTOM,
  level = 1,
}: SkeletonTitleProps): JSX.Element {
  
  /**
   * 根据标题级别调整高度
   */
  const getHeightByLevel = (level: number): string => {
    switch (level) {
      case 1: return SKELETON_SIZES.TITLE_HEIGHT;
      case 2: return 'size-500';
      case 3: return 'size-400';
      case 4: return 'size-350';
      case 5: return 'size-300';
      case 6: return 'size-250';
      default: return SKELETON_SIZES.TITLE_HEIGHT;
    }
  };

  const titleHeight = height === SKELETON_SIZES.TITLE_HEIGHT 
    ? getHeightByLevel(level) 
    : height;

  return (
    <View
      backgroundColor={SKELETON_STYLES.ITEM_BACKGROUND}
      borderRadius={SKELETON_STYLES.ITEM_BORDER_RADIUS}
      width={width}
      height={titleHeight}
      marginBottom={marginBottom}
      UNSAFE_className="skeleton-title"
      role="presentation"
      aria-hidden="true"
    />
  );
}

/**
 * 骨架屏标题组件的默认导出
 */
export default SkeletonTitle;
