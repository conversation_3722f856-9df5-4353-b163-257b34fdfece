/**
 * 骨架屏导航区域组件
 * 用于渲染导航按钮占位符
 */

import React from 'react';
import { View, Flex } from '@adobe/react-spectrum';

import type { SkeletonNavigationProps } from '../types';
import { 
  SKELETON_SIZES, 
  SKELETON_SPACING, 
  SKELETON_STYLES,
  RULE_INTRO_SKELETON_DEFAULTS 
} from '../constants';

/**
 * 骨架屏导航组件
 * 
 * 提供导航区域的骨架屏占位符，支持：
 * - 自定义按钮数量和宽度
 * - 灵活的对齐方式
 * - 统一的按钮样式
 * - 适用于导航栏、操作按钮组等场景
 * 
 * @param props 导航组件属性
 * @returns JSX.Element
 */
export function SkeletonNavigation({
  width = '100%',
  height = 'auto',
  marginBottom = SKELETON_SPACING.CONTENT_MARGIN_BOTTOM,
  buttonCount = RULE_INTRO_SKELETON_DEFAULTS.NAVIGATION_BUTTON_COUNT,
  buttonWidth = SKELETON_SIZES.NAV_BUTTON_WIDTH,
  justifyContent = 'center',
}: SkeletonNavigationProps): JSX.Element {
  
  /**
   * 渲染单个导航按钮
   */
  const renderNavigationButton = (index: number): JSX.Element => (
    <View
      key={`nav-button-${index}`}
      backgroundColor={SKELETON_STYLES.ITEM_BACKGROUND}
      borderRadius={SKELETON_STYLES.ITEM_BORDER_RADIUS}
      width={buttonWidth}
      height={SKELETON_SIZES.BUTTON_HEIGHT}
      UNSAFE_className="skeleton-nav-button"
      role="presentation"
      aria-hidden="true"
    />
  );

  /**
   * 生成所有导航按钮
   */
  const generateNavigationButtons = (): JSX.Element[] => {
    return Array.from({ length: buttonCount }, (_, index) => 
      renderNavigationButton(index)
    );
  };

  return (
    <View
      width={width}
      height={height}
      marginBottom={marginBottom}
      UNSAFE_className="skeleton-navigation"
      role="presentation"
      aria-hidden="true"
    >
      <Flex
        direction="row"
        justifyContent={justifyContent}
        alignItems="center"
        gap={SKELETON_SPACING.BUTTON_GAP}
        wrap="wrap"
      >
        {generateNavigationButtons()}
      </Flex>
    </View>
  );
}

/**
 * 骨架屏导航组件的默认导出
 */
export default SkeletonNavigation;
