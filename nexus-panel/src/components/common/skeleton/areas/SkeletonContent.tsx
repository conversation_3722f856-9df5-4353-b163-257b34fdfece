/**
 * 骨架屏内容区域组件
 * 用于渲染内容占位符
 */

import React from 'react';
import { View, Flex } from '@adobe/react-spectrum';

import type { SkeletonContentProps } from '../types';
import { 
  SKELETON_SIZES, 
  SKELETON_SPACING, 
  SKELETON_STYLES,
  SKELETON_CONTENT_LINES 
} from '../constants';

/**
 * 骨架屏内容组件
 * 
 * 提供内容区域的骨架屏占位符，支持：
 * - 多行内容渲染
 * - 自定义行数、高度和间距
 * - 不同宽度的行模拟真实内容
 * 
 * @param props 内容组件属性
 * @returns JSX.Element
 */
export function SkeletonContent({
  width = '100%',
  height = SKELETON_SIZES.CONTENT_HEIGHT,
  marginBottom = SKELETON_SPACING.CONTENT_MARGIN_BOTTOM,
  lines = SKELETON_CONTENT_LINES.STANDARD,
  spacing = SKELETON_SPACING.LINE_SPACING,
}: SkeletonContentProps): JSX.Element {
  
  /**
   * 生成内容行的宽度
   * 模拟真实内容的不同行长度
   */
  const getLineWidth = (index: number, total: number): string => {
    if (total === 1) return '100%';
    
    // 最后一行通常较短
    if (index === total - 1) return '75%';
    
    // 其他行的宽度变化
    const widths = ['100%', '95%', '90%', '100%', '85%'];
    return widths[index % widths.length];
  };

  /**
   * 渲染单行内容
   */
  const renderContentLine = (index: number): JSX.Element => (
    <View
      key={`content-line-${index}`}
      backgroundColor={SKELETON_STYLES.ITEM_BACKGROUND}
      borderRadius={SKELETON_STYLES.ITEM_BORDER_RADIUS}
      width={getLineWidth(index, lines)}
      height="size-200"
      marginBottom={index < lines - 1 ? spacing : '0'}
      UNSAFE_className="skeleton-content-line"
      role="presentation"
      aria-hidden="true"
    />
  );

  /**
   * 如果只有一行，渲染为单个块
   */
  if (lines === 1) {
    return (
      <View
        backgroundColor={SKELETON_STYLES.ITEM_BACKGROUND}
        borderRadius={SKELETON_STYLES.ITEM_BORDER_RADIUS}
        width={width}
        height={height}
        marginBottom={marginBottom}
        UNSAFE_className="skeleton-content-single"
        role="presentation"
        aria-hidden="true"
      />
    );
  }

  /**
   * 多行内容渲染
   */
  return (
    <Flex
      direction="column"
      width={width}
      marginBottom={marginBottom}
      UNSAFE_className="skeleton-content-multi"
      role="presentation"
      aria-hidden="true"
    >
      {Array.from({ length: lines }, (_, index) => renderContentLine(index))}
    </Flex>
  );
}

/**
 * 骨架屏内容组件的默认导出
 */
export default SkeletonContent;
