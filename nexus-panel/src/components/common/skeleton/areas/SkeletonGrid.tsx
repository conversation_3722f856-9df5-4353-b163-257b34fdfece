/**
 * 骨架屏网格区域组件
 * 用于渲染网格布局占位符
 */

import React from 'react';
import { View } from '@adobe/react-spectrum';

import type { SkeletonGridProps } from '../types';
import { 
  SKELETON_SIZES, 
  SKELETON_SPACING, 
  SKELETON_STYLES,
  SKELETON_GRID_CONFIG 
} from '../constants';

/**
 * 骨架屏网格组件
 * 
 * 提供网格布局的骨架屏占位符，支持：
 * - 自定义行列配置
 * - 灵活的网格间距
 * - 网格区域定义
 * - 适用于表格、列表等场景
 * 
 * @param props 网格组件属性
 * @returns JSX.Element
 */
export function SkeletonGrid({
  width = '100%',
  height = 'auto',
  marginBottom = SKELETON_SPACING.CONTENT_MARGIN_BOTTOM,
  rows = SKELETON_GRID_CONFIG.DEFAULT_ROWS,
  columns = SKELETON_GRID_CONFIG.DEFAULT_COLUMNS,
  gap = SKELETON_SPACING.GRID_GAP,
  areas,
}: SkeletonGridProps): JSX.Element {
  
  /**
   * 构建网格样式
   */
  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: columns.join(' '),
    gap: `var(--spectrum-global-dimension-${gap.replace('size-', 'static-size-')})`,
    width: '100%',
  };

  // 如果定义了网格区域，添加到样式中
  if (areas) {
    gridStyle.gridTemplateAreas = areas;
  }

  /**
   * 渲染网格项
   */
  const renderGridItem = (rowIndex: number, colIndex: number): JSX.Element => {
    const itemKey = `grid-item-${rowIndex}-${colIndex}`;
    
    return (
      <View
        key={itemKey}
        backgroundColor={SKELETON_STYLES.ITEM_BACKGROUND}
        borderRadius={SKELETON_STYLES.ITEM_BORDER_RADIUS}
        height={SKELETON_SIZES.GRID_ITEM_HEIGHT}
        UNSAFE_className="skeleton-grid-item"
        role="presentation"
        aria-hidden="true"
      />
    );
  };

  /**
   * 生成所有网格项
   */
  const generateGridItems = (): JSX.Element[] => {
    const items: JSX.Element[] = [];
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < columns.length; col++) {
        items.push(renderGridItem(row, col));
      }
    }
    
    return items;
  };

  return (
    <View
      width={width}
      height={height}
      marginBottom={marginBottom}
      UNSAFE_className="skeleton-grid"
      UNSAFE_style={gridStyle}
      role="presentation"
      aria-hidden="true"
    >
      {generateGridItems()}
    </View>
  );
}

/**
 * 骨架屏网格组件的默认导出
 */
export default SkeletonGrid;
