/**
 * 骨架屏组件类型定义
 * 统一所有骨架屏组件的接口和类型
 */

import { ReactNode } from 'react';

/**
 * 骨架屏基础组件属性接口
 */
export interface SkeletonBaseProps {
  /** 自定义CSS类名 */
  className?: string;
  /** 是否启用动画效果 */
  animated?: boolean;
  /** 组件高度 */
  height?: string;
  /** 是否支持交互 */
  interactive?: boolean;
  /** 加载状态 */
  status?: SkeletonStatus;
  /** 淡入淡出效果 */
  fadeEffect?: SkeletonFadeEffect;
  /** 子组件内容 */
  children: ReactNode;
}

/**
 * 骨架屏区域组件基础属性
 */
export interface SkeletonAreaProps {
  /** 宽度 */
  width?: string;
  /** 高度 */
  height?: string;
  /** 底部边距 */
  marginBottom?: string;
}

/**
 * 骨架屏标题组件属性
 */
export interface SkeletonTitleProps extends SkeletonAreaProps {
  /** 标题级别 */
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

/**
 * 骨架屏内容组件属性
 */
export interface SkeletonContentProps extends SkeletonAreaProps {
  /** 内容行数 */
  lines?: number;
  /** 行间距 */
  spacing?: string;
}

/**
 * 骨架屏网格组件属性
 */
export interface SkeletonGridProps extends SkeletonAreaProps {
  /** 行数 */
  rows?: number;
  /** 列配置 */
  columns?: string[];
  /** 网格间距 */
  gap?: string;
  /** 网格区域定义 */
  areas?: string;
}

/**
 * 骨架屏导航组件属性
 */
export interface SkeletonNavigationProps extends SkeletonAreaProps {
  /** 按钮数量 */
  buttonCount?: number;
  /** 按钮宽度 */
  buttonWidth?: string;
  /** 对齐方式 */
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around';
}

/**
 * 问题骨架屏特殊属性
 */
export interface QuestionSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  /** 是否显示排名区域 */
  showRankingArea?: boolean;
  /** 排名行数 */
  rankingRowCount?: number;
}

/**
 * 排名骨架屏特殊属性
 */
export interface RankingSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  /** 动态行数 */
  dynamicRowCount?: number;
  /** 是否显示头部 */
  showHeader?: boolean;
}

/**
 * 规则介绍骨架屏特殊属性
 */
export interface RuleIntroSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  /** 是否显示导航按钮 */
  showNavigation?: boolean;
  /** 导航按钮数量 */
  navigationButtonCount?: number;
}

/**
 * 终极PK骨架屏特殊属性
 */
export interface UltimatePKSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  /** PK布局类型 */
  pkLayoutType?: 'standard' | 'compact' | 'extended';
  /** 是否显示对战信息 */
  showBattleInfo?: boolean;
}

/**
 * 首页和安全课程骨架屏属性（共用简单结构）
 */
export interface SimpleSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  /** 内容类型 */
  contentType?: 'home' | 'safety' | 'generic';
}

/**
 * 骨架屏状态枚举
 */
export type SkeletonStatus = 'loading' | 'error' | 'success';

/**
 * 骨架屏淡入淡出效果枚举
 */
export type SkeletonFadeEffect = 'fade-in' | 'fade-out' | null;

/**
 * 骨架屏动画类型
 */
export type SkeletonAnimationType = 'pulse' | 'wave' | 'shimmer' | 'none';

/**
 * 骨架屏主题类型
 */
export type SkeletonTheme = 'light' | 'dark' | 'auto';

/**
 * 骨架屏配置接口
 */
export interface SkeletonConfig {
  /** 默认动画类型 */
  defaultAnimation: SkeletonAnimationType;
  /** 默认主题 */
  defaultTheme: SkeletonTheme;
  /** 是否启用全局动画 */
  globalAnimationEnabled: boolean;
  /** 动画持续时间 */
  animationDuration: number;
}
