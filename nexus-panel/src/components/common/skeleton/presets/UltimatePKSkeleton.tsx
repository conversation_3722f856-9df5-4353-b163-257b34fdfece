/**
 * 终极PK骨架屏预设组件
 * 使用分层组合模式重构的终极PK骨架屏
 */

import React from 'react';
import { Flex } from '@adobe/react-spectrum';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonContent, SkeletonGrid } from '../areas';
import type { UltimatePKSkeletonProps } from '../types';
import { 
  SKELETON_PRESETS,
  ULTIMATE_PK_SKELETON_DEFAULTS,
  SKELETON_GRID_CONFIG 
} from '../constants';

/**
 * 终极PK骨架屏组件
 * 
 * 使用基础组件组合实现终极PK页面的骨架屏效果
 * 支持不同的PK布局类型和对战信息显示
 * 
 * @param props 终极PK骨架屏属性
 * @returns JSX.Element
 */
export function UltimatePKSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  pkLayoutType = ULTIMATE_PK_SKELETON_DEFAULTS.PK_LAYOUT_TYPE,
  showBattleInfo = ULTIMATE_PK_SKELETON_DEFAULTS.SHOW_BATTLE_INFO,
}: UltimatePKSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS.ultimatePK;

  /**
   * 渲染PK对战区域
   */
  const renderPKBattleArea = (): JSX.Element => {
    switch (pkLayoutType) {
      case 'compact':
        return (
          <SkeletonGrid
            rows={2}
            columns={['1fr', 'auto', '1fr']}
            gap="size-300"
          />
        );
      case 'extended':
        return (
          <SkeletonGrid
            rows={3}
            columns={['1fr', 'auto', '1fr']}
            gap="size-400"
          />
        );
      default: // standard
        return (
          <Flex direction="row" gap="size-400" justifyContent="space-between">
            <SkeletonContent lines={2} width="45%" />
            <SkeletonContent lines={1} width="10%" />
            <SkeletonContent lines={2} width="45%" />
          </Flex>
        );
    }
  };

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      <SkeletonTitle />
      
      {showBattleInfo && (
        <SkeletonContent 
          lines={1}
          marginBottom="size-400"
        />
      )}
      
      {renderPKBattleArea()}
      
      <SkeletonContent 
        lines={preset.contentLines - 3}
        marginBottom="0"
      />
    </SkeletonBase>
  );
}

/**
 * 终极PK骨架屏组件的默认导出
 */
export default UltimatePKSkeleton;
