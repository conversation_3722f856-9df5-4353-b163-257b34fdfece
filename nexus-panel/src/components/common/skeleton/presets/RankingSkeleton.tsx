/**
 * 排名骨架屏预设组件
 * 使用分层组合模式重构的排名骨架屏
 */

import React from 'react';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonGrid } from '../areas';
import type { RankingSkeletonProps } from '../types';
import { 
  SKELETON_PRESETS, 
  SKELETON_GRID_CONFIG,
  RANKING_SKELETON_DEFAULTS 
} from '../constants';

/**
 * 排名骨架屏组件
 * 
 * 使用基础组件组合实现排名页面的骨架屏效果
 * 支持动态行数和可选的头部显示
 * 
 * @param props 排名骨架屏属性
 * @returns JSX.Element
 */
export function RankingSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  dynamicRowCount = RANKING_SKELETON_DEFAULTS.DYNAMIC_ROW_COUNT,
  showHeader = RANKING_SKELETON_DEFAULTS.SHOW_HEADER,
}: RankingSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS.ranking;

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      {showHeader && (
        <SkeletonTitle />
      )}
      
      <SkeletonGrid
        rows={dynamicRowCount}
        columns={SKELETON_GRID_CONFIG.RANKING_COLUMNS}
        gap="size-150"
      />
    </SkeletonBase>
  );
}

/**
 * 排名骨架屏组件的默认导出
 */
export default RankingSkeleton;
