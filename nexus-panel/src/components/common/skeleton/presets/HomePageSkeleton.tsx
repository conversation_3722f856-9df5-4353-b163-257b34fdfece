/**
 * 首页骨架屏预设组件
 * 使用分层组合模式重构的首页骨架屏
 */

import React from 'react';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonContent } from '../areas';
import type { SimpleSkeletonProps } from '../types';
import { SKELETON_PRESETS } from '../constants';

/**
 * 首页骨架屏组件
 * 
 * 使用基础组件组合实现首页的骨架屏效果
 * 包含标题和简单内容区域
 * 
 * @param props 简单骨架屏属性
 * @returns JSX.Element
 */
export function HomePageSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  contentType = 'home',
}: SimpleSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS[contentType];

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      {preset.showTitle && (
        <SkeletonTitle />
      )}
      <SkeletonContent 
        lines={preset.contentLines}
      />
    </SkeletonBase>
  );
}

/**
 * 首页骨架屏组件的默认导出
 */
export default HomePageSkeleton;
