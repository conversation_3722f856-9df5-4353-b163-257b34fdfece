/**
 * 规则介绍骨架屏预设组件
 * 使用分层组合模式重构的规则介绍骨架屏
 */

import React from 'react';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonContent, SkeletonNavigation } from '../areas';
import type { RuleIntroSkeletonProps } from '../types';
import { 
  SKELETON_PRESETS,
  RULE_INTRO_SKELETON_DEFAULTS 
} from '../constants';

/**
 * 规则介绍骨架屏组件
 * 
 * 使用基础组件组合实现规则介绍页面的骨架屏效果
 * 支持可选的导航按钮显示
 * 
 * @param props 规则介绍骨架屏属性
 * @returns JSX.Element
 */
export function RuleIntroSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  showNavigation = RULE_INTRO_SKELETON_DEFAULTS.SHOW_NAVIGATION,
  navigationButtonCount = RULE_INTRO_SKELETON_DEFAULTS.NAVIGATION_BUTTON_COUNT,
}: RuleIntroSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS.rules;

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      {preset.showTitle && (
        <SkeletonTitle />
      )}
      
      <SkeletonContent 
        lines={preset.contentLines}
      />
      
      {showNavigation && (
        <SkeletonNavigation
          buttonCount={navigationButtonCount}
          justifyContent="center"
        />
      )}
    </SkeletonBase>
  );
}

/**
 * 规则介绍骨架屏组件的默认导出
 */
export default RuleIntroSkeleton;
