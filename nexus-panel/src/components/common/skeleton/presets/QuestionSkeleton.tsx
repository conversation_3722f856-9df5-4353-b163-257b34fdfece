/**
 * 题目骨架屏预设组件
 * 使用分层组合模式重构的题目骨架屏
 */

import React from 'react';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonContent, SkeletonGrid } from '../areas';
import type { QuestionSkeletonProps } from '../types';
import { 
  SKELETON_PRESETS, 
  SKELETON_GRID_CONFIG,
  QUESTION_SKELETON_DEFAULTS 
} from '../constants';

/**
 * 题目骨架屏组件
 * 
 * 使用基础组件组合实现题目页面的骨架屏效果
 * 支持可选的排名区域显示
 * 
 * @param props 题目骨架屏属性
 * @returns JSX.Element
 */
export function QuestionSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  showRankingArea = QUESTION_SKELETON_DEFAULTS.SHOW_RANKING_AREA,
  rankingRowCount = QUESTION_SKELETON_DEFAULTS.RANKING_ROW_COUNT,
}: QuestionSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS.question;

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      {preset.showTitle && (
        <SkeletonTitle />
      )}
      
      <SkeletonContent 
        lines={preset.contentLines}
      />
      
      {showRankingArea && (
        <SkeletonGrid
          rows={rankingRowCount}
          columns={SKELETON_GRID_CONFIG.RANKING_COLUMNS}
          marginBottom="size-400"
        />
      )}
    </SkeletonBase>
  );
}

/**
 * 题目骨架屏组件的默认导出
 */
export default QuestionSkeleton;
