/**
 * 安全公开课骨架屏预设组件
 * 使用分层组合模式重构的安全课程骨架屏
 */

import React from 'react';

import { SkeletonBase } from '../SkeletonBase';
import { SkeletonTitle, SkeletonContent } from '../areas';
import type { SimpleSkeletonProps } from '../types';
import { SKELETON_PRESETS } from '../constants';

/**
 * 安全公开课骨架屏组件
 * 
 * 使用基础组件组合实现安全课程的骨架屏效果
 * 与首页骨架屏结构相同，但使用不同的内容类型
 * 
 * @param props 简单骨架屏属性
 * @returns JSX.Element
 */
export function SafetyPublicClassSkeleton({
  className,
  animated,
  height,
  interactive,
  status,
  fadeEffect,
  contentType = 'safety',
}: SimpleSkeletonProps): JSX.Element {
  
  const preset = SKELETON_PRESETS[contentType];

  return (
    <SkeletonBase
      className={className}
      animated={animated}
      height={height}
      interactive={interactive}
      status={status}
      fadeEffect={fadeEffect}
    >
      {preset.showTitle && (
        <SkeletonTitle />
      )}
      <SkeletonContent 
        lines={preset.contentLines}
      />
    </SkeletonBase>
  );
}

/**
 * 安全公开课骨架屏组件的默认导出
 */
export default SafetyPublicClassSkeleton;
