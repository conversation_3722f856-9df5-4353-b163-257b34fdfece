/**
 * 骨架屏基础组件
 * 提供统一的骨架屏容器和样式管理
 */

import React from 'react';
import { View } from '@adobe/react-spectrum';

import type { SkeletonBaseProps } from './types';
import { 
  SKELETON_DEFAULTS, 
  SKELETON_STYLES, 
  SKELETON_CSS_CLASSES 
} from './constants';

/**
 * 骨架屏基础组件
 * 
 * 统一管理所有骨架屏组件的通用功能：
 * - CSS类名构建逻辑
 * - 标准化View容器配置
 * - 通用动画和状态管理
 * - 子组件渲染支持
 * 
 * @param props 骨架屏基础属性
 * @returns JSX.Element
 */
export function SkeletonBase({
  className,
  animated = SKELETON_DEFAULTS.ANIMATED,
  height = SKELETON_DEFAULTS.HEIGHT,
  interactive = SKELETON_DEFAULTS.INTERACTIVE,
  status = SKELETON_DEFAULTS.STATUS,
  fadeEffect = SKELETON_DEFAULTS.FADE_EFFECT,
  children,
}: SkeletonBaseProps): JSX.Element {
  
  /**
   * 构建CSS类名
   * 统一的类名构建逻辑，确保所有骨架屏组件的样式一致性
   */
  const cssClasses = [
    SKELETON_CSS_CLASSES.BASE,
    animated ? SKELETON_CSS_CLASSES.ANIMATED : '',
    interactive ? SKELETON_CSS_CLASSES.INTERACTIVE : '',
    status !== 'loading' ? SKELETON_CSS_CLASSES[status.toUpperCase() as keyof typeof SKELETON_CSS_CLASSES] : '',
    fadeEffect ? SKELETON_CSS_CLASSES[fadeEffect.toUpperCase().replace('-', '_') as keyof typeof SKELETON_CSS_CLASSES] : '',
    className || '',
  ].filter(Boolean).join(' ');

  /**
   * 渲染骨架屏基础容器
   * 使用Adobe React Spectrum的View组件作为容器
   * 应用统一的样式配置和动态类名
   */
  return (
    <View
      backgroundColor={SKELETON_STYLES.BACKGROUND_COLOR}
      borderRadius={SKELETON_STYLES.BORDER_RADIUS}
      padding={SKELETON_STYLES.PADDING}
      height={height}
      UNSAFE_className={cssClasses}
      role="status"
      aria-label="内容加载中"
      aria-live="polite"
    >
      {children}
    </View>
  );
}

/**
 * 骨架屏基础组件的默认导出
 */
export default SkeletonBase;
