/**
 * 骨架屏组件常量定义
 * 统一管理所有骨架屏相关的默认值和样式常量
 */

import type { SkeletonConfig, SkeletonStatus, SkeletonFadeEffect } from './types';

/**
 * 骨架屏默认属性值
 */
export const SKELETON_DEFAULTS = {
  /** 默认启用动画 */
  ANIMATED: true,
  /** 默认高度 */
  HEIGHT: 'auto',
  /** 默认不支持交互 */
  INTERACTIVE: false,
  /** 默认加载状态 */
  STATUS: 'loading' as SkeletonStatus,
  /** 默认无淡入淡出效果 */
  FADE_EFFECT: null as SkeletonFadeEffect,
} as const;

/**
 * 骨架屏样式常量
 */
export const SKELETON_STYLES = {
  /** 背景颜色 */
  BACKGROUND_COLOR: 'gray-100',
  /** 边框圆角 */
  BORDER_RADIUS: 'medium',
  /** 内边距 */
  PADDING: 'size-300',
  /** 骨架屏项目背景色 */
  ITEM_BACKGROUND: 'gray-300',
  /** 骨架屏项目边框圆角 */
  ITEM_BORDER_RADIUS: 'small',
  /** 动画持续时间 */
  ANIMATION_DURATION: '2s',
  /** 动画时间函数 */
  ANIMATION_TIMING: 'ease-in-out',
} as const;

/**
 * 骨架屏尺寸常量
 */
export const SKELETON_SIZES = {
  /** 标题高度 */
  TITLE_HEIGHT: 'size-600',
  /** 内容高度 */
  CONTENT_HEIGHT: 'size-3000',
  /** 小内容高度 */
  CONTENT_HEIGHT_SMALL: 'size-1000',
  /** 按钮高度 */
  BUTTON_HEIGHT: 'size-400',
  /** 按钮宽度 */
  BUTTON_WIDTH: 'size-1200',
  /** 网格项高度 */
  GRID_ITEM_HEIGHT: 'size-800',
  /** 导航按钮宽度 */
  NAV_BUTTON_WIDTH: 'size-1000',
} as const;

/**
 * 骨架屏间距常量
 */
export const SKELETON_SPACING = {
  /** 标题底部边距 */
  TITLE_MARGIN_BOTTOM: 'size-400',
  /** 内容底部边距 */
  CONTENT_MARGIN_BOTTOM: 'size-300',
  /** 行间距 */
  LINE_SPACING: 'size-200',
  /** 网格间距 */
  GRID_GAP: 'size-200',
  /** 按钮间距 */
  BUTTON_GAP: 'size-150',
} as const;

/**
 * 骨架屏CSS类名常量
 */
export const SKELETON_CSS_CLASSES = {
  /** 基础类名 */
  BASE: 'component-skeleton',
  /** 动画类名 */
  ANIMATED: 'animated',
  /** 交互类名 */
  INTERACTIVE: 'interactive',
  /** 加载状态类名 */
  LOADING: 'loading',
  /** 错误状态类名 */
  ERROR: 'error',
  /** 成功状态类名 */
  SUCCESS: 'success',
  /** 淡入效果类名 */
  FADE_IN: 'fade-in',
  /** 淡出效果类名 */
  FADE_OUT: 'fade-out',
} as const;

/**
 * 问题骨架屏默认配置
 */
export const QUESTION_SKELETON_DEFAULTS = {
  /** 默认显示排名区域 */
  SHOW_RANKING_AREA: false,
  /** 默认排名行数 */
  RANKING_ROW_COUNT: 5,
} as const;

/**
 * 排名骨架屏默认配置
 */
export const RANKING_SKELETON_DEFAULTS = {
  /** 默认动态行数 */
  DYNAMIC_ROW_COUNT: 10,
  /** 默认显示头部 */
  SHOW_HEADER: true,
} as const;

/**
 * 规则介绍骨架屏默认配置
 */
export const RULE_INTRO_SKELETON_DEFAULTS = {
  /** 默认显示导航 */
  SHOW_NAVIGATION: true,
  /** 默认导航按钮数量 */
  NAVIGATION_BUTTON_COUNT: 3,
} as const;

/**
 * 终极PK骨架屏默认配置
 */
export const ULTIMATE_PK_SKELETON_DEFAULTS = {
  /** 默认PK布局类型 */
  PK_LAYOUT_TYPE: 'standard' as const,
  /** 默认显示对战信息 */
  SHOW_BATTLE_INFO: true,
} as const;

/**
 * 骨架屏内容行数配置
 */
export const SKELETON_CONTENT_LINES = {
  /** 简单内容行数 */
  SIMPLE: 3,
  /** 标准内容行数 */
  STANDARD: 5,
  /** 详细内容行数 */
  DETAILED: 8,
  /** 最大内容行数 */
  MAX: 12,
} as const;

/**
 * 骨架屏网格配置
 */
export const SKELETON_GRID_CONFIG = {
  /** 默认行数 */
  DEFAULT_ROWS: 3,
  /** 默认列配置 */
  DEFAULT_COLUMNS: ['1fr', '2fr', '1fr'],
  /** 排名网格列配置 */
  RANKING_COLUMNS: ['auto', '1fr', 'auto', 'auto'],
  /** 问题网格列配置 */
  QUESTION_COLUMNS: ['1fr'],
} as const;

/**
 * 全局骨架屏配置
 */
export const SKELETON_GLOBAL_CONFIG: SkeletonConfig = {
  /** 默认动画类型 */
  defaultAnimation: 'pulse',
  /** 默认主题 */
  defaultTheme: 'light',
  /** 启用全局动画 */
  globalAnimationEnabled: true,
  /** 动画持续时间（毫秒） */
  animationDuration: 2000,
} as const;

/**
 * 骨架屏预设配置映射
 */
export const SKELETON_PRESETS = {
  home: {
    contentLines: SKELETON_CONTENT_LINES.SIMPLE,
    showTitle: true,
    showNavigation: false,
  },
  safety: {
    contentLines: SKELETON_CONTENT_LINES.SIMPLE,
    showTitle: true,
    showNavigation: false,
  },
  question: {
    contentLines: SKELETON_CONTENT_LINES.STANDARD,
    showTitle: true,
    showRankingArea: false,
    rankingRowCount: QUESTION_SKELETON_DEFAULTS.RANKING_ROW_COUNT,
  },
  ranking: {
    contentLines: SKELETON_CONTENT_LINES.DETAILED,
    showHeader: RANKING_SKELETON_DEFAULTS.SHOW_HEADER,
    dynamicRowCount: RANKING_SKELETON_DEFAULTS.DYNAMIC_ROW_COUNT,
  },
  rules: {
    contentLines: SKELETON_CONTENT_LINES.STANDARD,
    showTitle: true,
    showNavigation: RULE_INTRO_SKELETON_DEFAULTS.SHOW_NAVIGATION,
    navigationButtonCount: RULE_INTRO_SKELETON_DEFAULTS.NAVIGATION_BUTTON_COUNT,
  },
  ultimatePK: {
    contentLines: SKELETON_CONTENT_LINES.DETAILED,
    pkLayoutType: ULTIMATE_PK_SKELETON_DEFAULTS.PK_LAYOUT_TYPE,
    showBattleInfo: ULTIMATE_PK_SKELETON_DEFAULTS.SHOW_BATTLE_INFO,
  },
} as const;
