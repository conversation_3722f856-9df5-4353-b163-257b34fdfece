/**
 * 业务API函数
 * 
 * 将原本分散在各个服务类中的API调用整合为简单的函数
 * 使用新的simpleFetch工具，消除重复的错误处理和配置
 */

import { get } from '../utils/api/simpleFetch';
import { API_CONFIG } from '../config/api/apiConfig';
import type { 
  RaceApiResponse, 
  RaceApiItem,
  NavigationResponse,
  TableStructureResponse,
  SectionDataResponse,
  ConfigurationDataResponse,
  RulesIntroductionApiResponse,
  QuestionResponse,
  RankingResponse,
  PlayerInfoApiResponse,
  AnswerRecordApiResponse
} from '../services/api/types';

// ==================== 赛事相关API ====================

/**
 * 获取赛事列表
 * 替代 RaceApiService.getRaceListRaw()
 */
export const getRaces = async (): Promise<RaceApiResponse> => {
  const response = await get<RaceApiResponse>(API_CONFIG.endpoints.races, {
    viewId: API_CONFIG.endpoints.raceView,
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取可见的赛事列表
 * 替代 getVisibleRaces()
 */
export const getVisibleRaces = async (): Promise<RaceApiItem[]> => {
  const response = await getRaces();
  return response.list.filter(race => race['是否显示'] === '显示');
};

/**
 * 根据ID获取特定赛事
 * @param raceId 赛事ID
 */
export const getRaceById = async (raceId: string): Promise<RaceApiItem | null> => {
  const races = await getVisibleRaces();
  return races.find(race => race['赛事 ID'] === raceId) || null;
};

/**
 * 检查赛事服务健康状态
 * 替代 checkRaceServiceHealth()
 */
export const checkRaceServiceHealth = async (): Promise<boolean> => {
  try {
    await getRaces();
    return true;
  } catch {
    return false;
  }
};

// ==================== 导航相关API ====================

/**
 * 获取表结构信息
 * 替代 NavigationApiService.getTableStructure()
 * @param baseId 表ID
 */
export const getTableStructure = async (baseId: string): Promise<TableStructureResponse> => {
  const endpoint = `/meta/bases/${baseId}/tables`;
  const response = await get<TableStructureResponse>(endpoint);
  return response.data;
};

/**
 * 获取环节数据
 * 替代 NavigationApiService.getSectionData()
 * @param baseId 表ID
 */
export const getSectionData = async (baseId: string): Promise<SectionDataResponse> => {
  const endpoint = API_CONFIG.endpoints.navigation.replace('{baseId}', baseId);
  const response = await get<SectionDataResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取导航数据
 * 替代 getNavigationData()
 * @param baseId 表ID
 */
export const getNavigationData = async (baseId: string) => {
  const sectionData = await getSectionData(baseId);
  // 这里可以添加数据转换逻辑
  return sectionData;
};

/**
 * 获取配置信息数据
 * 替代 NavigationApiService.getConfigurationData()
 * @param baseId 表ID
 */
export const getConfigurationData = async (baseId: string): Promise<ConfigurationDataResponse> => {
  const endpoint = API_CONFIG.endpoints.navigation.replace('{baseId}', baseId);
  const response = await get<ConfigurationDataResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取规则介绍数据
 * 替代 NavigationApiService.getRulesIntroductionData()
 * @param baseId 表ID
 */
export const getRulesIntroductionData = async (baseId: string): Promise<RulesIntroductionApiResponse> => {
  const endpoint = API_CONFIG.endpoints.navigation.replace('{baseId}', baseId);
  const response = await get<RulesIntroductionApiResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

// ==================== 题目相关API ====================

/**
 * 获取题目数据
 * 替代 QuestionApiService.getQuestionData()
 * @param baseId 表ID
 * @param stage 可选的阶段筛选
 */
export const getQuestions = async (
  baseId: string, 
  stage?: string
): Promise<QuestionResponse> => {
  const endpoint = API_CONFIG.endpoints.questions.replace('{baseId}', baseId);
  const params: Record<string, string | number> = {
    limit: API_CONFIG.defaults.limit
  };
  
  // 如果指定了阶段，添加筛选条件
  if (stage) {
    params.where = `(阶段,eq,${encodeURIComponent(stage)})`;
  }
  
  const response = await get<QuestionResponse>(endpoint, params);
  return response.data;
};

/**
 * 检查题目服务健康状态
 * @param baseId 表ID
 */
export const checkQuestionServiceHealth = async (baseId: string): Promise<boolean> => {
  try {
    await getQuestions(baseId);
    return true;
  } catch {
    return false;
  }
};

// ==================== 排名相关API ====================

/**
 * 获取答题记录数据
 * 替代 RankingApiService.getAnswerRecords()
 * @param baseId 表ID
 */
export const getAnswerRecords = async (baseId: string): Promise<AnswerRecordApiResponse> => {
  const endpoint = API_CONFIG.endpoints.ranking.replace('{baseId}', baseId);
  const response = await get<AnswerRecordApiResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取选手信息数据
 * 替代 RankingApiService.getPlayerInfo()
 * @param baseId 表ID
 */
export const getPlayerInfo = async (baseId: string): Promise<PlayerInfoApiResponse> => {
  const endpoint = API_CONFIG.endpoints.ranking.replace('{baseId}', baseId);
  const response = await get<PlayerInfoApiResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取排名数据
 * 替代 getRankingData()
 * @param baseId 表ID
 */
export const getRankingData = async (baseId: string): Promise<RankingResponse> => {
  const endpoint = API_CONFIG.endpoints.ranking.replace('{baseId}', baseId);
  const response = await get<RankingResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit
  });
  return response.data;
};

/**
 * 获取环节排名数据
 * 替代 getSectionRankingData()
 * @param baseId 表ID
 * @param sectionName 环节名称
 */
export const getSectionRankingData = async (
  baseId: string, 
  sectionName: string
): Promise<RankingResponse> => {
  const endpoint = API_CONFIG.endpoints.ranking.replace('{baseId}', baseId);
  const response = await get<RankingResponse>(endpoint, {
    limit: API_CONFIG.defaults.limit,
    where: `(环节,eq,${encodeURIComponent(sectionName)})`
  });
  return response.data;
};

// ==================== 健康检查API ====================

/**
 * 检查导航服务健康状态
 * @param baseId 表ID
 */
export const checkNavigationServiceHealth = async (baseId: string): Promise<boolean> => {
  try {
    await getNavigationData(baseId);
    return true;
  } catch {
    return false;
  }
};

/**
 * 检查所有服务的健康状态
 * @param baseId 表ID
 */
export const checkAllServicesHealth = async (baseId?: string): Promise<{
  race: boolean;
  navigation: boolean;
  question: boolean;
}> => {
  const results = await Promise.allSettled([
    checkRaceServiceHealth(),
    baseId ? checkNavigationServiceHealth(baseId) : Promise.resolve(false),
    baseId ? checkQuestionServiceHealth(baseId) : Promise.resolve(false)
  ]);

  return {
    race: results[0].status === 'fulfilled' ? results[0].value : false,
    navigation: results[1].status === 'fulfilled' ? results[1].value : false,
    question: results[2].status === 'fulfilled' ? results[2].value : false
  };
};
