/**
 * 未使用导入清理器
 * 自动检测和清理项目中的未使用导入
 */

const fs = require('fs');
const path = require('path');
const { ESLint } = require('eslint');

class UnusedImportsCleaner {
  constructor(options = {}) {
    this.srcDir = options.srcDir || 'src';
    this.extensions = options.extensions || ['.ts', '.tsx', '.js', '.jsx'];
    this.dryRun = options.dryRun || false;
    this.verbose = options.verbose || false;
    
    this.eslint = new ESLint({
      configFile: path.join(__dirname, '../quality-tools/eslint-config-custom.js'),
      fix: !this.dryRun,
      useEslintrc: false
    });
  }

  /**
   * 清理未使用的导入
   */
  async cleanUnusedImports() {
    console.log(`🧹 开始清理未使用的导入...`);
    console.log(`📁 扫描目录: ${this.srcDir}`);
    console.log(`🔧 模式: ${this.dryRun ? '预览模式' : '修复模式'}`);
    
    const files = this.getAllFiles(this.srcDir);
    console.log(`📄 找到 ${files.length} 个文件`);
    
    const results = [];
    let totalChanges = 0;

    for (const file of files) {
      try {
        const result = await this.processFile(file);
        if (result.changes > 0) {
          results.push(result);
          totalChanges += result.changes;
          
          if (this.verbose) {
            console.log(`✅ ${file}: 清理了 ${result.changes} 个未使用的导入`);
          }
        }
      } catch (error) {
        console.error(`❌ 处理文件失败 ${file}:`, error.message);
      }
    }

    const report = this.generateReport(results, totalChanges);
    this.printReport(report);
    
    return report;
  }

  /**
   * 处理单个文件
   */
  async processFile(filePath) {
    const results = await this.eslint.lintFiles([filePath]);
    const result = results[0];
    
    if (!result) return { file: filePath, changes: 0, messages: [] };

    const unusedImportErrors = result.messages.filter(
      msg => msg.ruleId === 'unused-imports/no-unused-imports'
    );

    // 如果不是预览模式且有修复内容，写入文件
    if (!this.dryRun && result.output) {
      fs.writeFileSync(filePath, result.output);
    }

    return {
      file: filePath,
      changes: unusedImportErrors.length,
      messages: unusedImportErrors,
      fixed: !this.dryRun && result.output !== undefined
    };
  }

  /**
   * 获取所有需要处理的文件
   */
  getAllFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !this.shouldIgnoreDirectory(item)) {
        files.push(...this.getAllFiles(fullPath));
      } else if (stat.isFile() && this.shouldProcessFile(item)) {
        files.push(fullPath);
      }
    }

    return files;
  }

  /**
   * 判断是否应该忽略目录
   */
  shouldIgnoreDirectory(dirName) {
    const ignoreDirs = [
      'node_modules',
      '.git',
      'dist',
      'build',
      'coverage',
      '.next',
      '.vscode',
      '.idea'
    ];
    return dirName.startsWith('.') || ignoreDirs.includes(dirName);
  }

  /**
   * 判断是否应该处理文件
   */
  shouldProcessFile(fileName) {
    return this.extensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * 生成清理报告
   */
  generateReport(results, totalChanges) {
    return {
      summary: {
        totalFiles: results.length,
        totalChanges,
        dryRun: this.dryRun,
        timestamp: new Date().toISOString()
      },
      details: results
    };
  }

  /**
   * 打印清理报告
   */
  printReport(report) {
    console.log('\n📊 清理报告');
    console.log('='.repeat(50));
    console.log(`📁 处理文件数: ${report.summary.totalFiles}`);
    console.log(`🧹 清理导入数: ${report.summary.totalChanges}`);
    console.log(`🔧 执行模式: ${report.summary.dryRun ? '预览模式' : '修复模式'}`);
    console.log(`⏰ 执行时间: ${report.summary.timestamp}`);
    
    if (report.details.length > 0) {
      console.log('\n📄 详细信息:');
      report.details.forEach(detail => {
        console.log(`  ${detail.file}: ${detail.changes} 个未使用导入`);
      });
    }
    
    if (report.summary.totalChanges > 0) {
      console.log(`\n✅ 成功清理了 ${report.summary.totalChanges} 个未使用的导入！`);
    } else {
      console.log('\n✨ 没有发现未使用的导入，代码很干净！');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const cleaner = new UnusedImportsCleaner({
    srcDir: 'src',
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose')
  });
  
  cleaner.cleanUnusedImports()
    .then(() => {
      console.log('\n🎉 清理完成！');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ 清理失败:', error);
      process.exit(1);
    });
}

module.exports = UnusedImportsCleaner;
