# API架构重构实施计划

## 📋 实施概览
**目标**: 统一API Hook架构和服务层中间件
**预计节省**: 1000行代码
**实施风险**: 中风险
**预计时间**: 14小时 (Hook层6小时 + 服务层8小时)

## 🗂️ 文件结构规划

### Hook层重构
```
src/hooks/
├── api/
│   ├── useUnifiedApi.ts          # 统一API Hook
│   ├── configs/
│   │   ├── apiConfigs.ts         # API配置定义
│   │   ├── retryConfigs.ts       # 重试配置
│   │   ├── cacheConfigs.ts       # 缓存配置
│   │   └── index.ts              # 配置导出
│   ├── plugins/
│   │   ├── retryPlugin.ts        # 重试插件
│   │   ├── cachePlugin.ts        # 缓存插件
│   │   ├── pollingPlugin.ts      # 轮询插件
│   │   ├── healthCheckPlugin.ts  # 健康检查插件
│   │   └── index.ts              # 插件导出
│   ├── types.ts                  # API Hook类型定义
│   └── index.ts                  # 统一导出
├── useApi.ts                     # 重构为使用统一架构
├── useRaceApi/                   # 重构现有模块
│   ├── useRaceApi.ts             # 简化为配置化调用
│   ├── configs.ts                # 赛事API专用配置
│   └── index.ts                  # 更新导出
└── useTimeRaceRanking.ts         # 重构为使用统一架构
```

### 服务层重构
```
src/services/api/
├── middleware/
│   ├── authMiddleware.ts         # 认证中间件
│   ├── errorHandlingMiddleware.ts # 错误处理中间件
│   ├── loggingMiddleware.ts      # 日志中间件
│   ├── cacheMiddleware.ts        # 缓存中间件
│   ├── retryMiddleware.ts        # 重试中间件
│   ├── types.ts                  # 中间件类型定义
│   └── index.ts                  # 中间件导出
├── core/
│   ├── ApiService.ts             # 核心API服务类
│   ├── MiddlewareManager.ts      # 中间件管理器
│   ├── ConfigManager.ts          # 配置管理器
│   └── index.ts                  # 核心模块导出
├── config/
│   ├── apiConfig.ts              # 统一API配置
│   ├── middlewareConfig.ts       # 中间件配置
│   └── index.ts                  # 配置导出
├── navigationApi.ts              # 重构为使用中间件
├── questionApi.ts                # 重构为使用中间件
├── raceApi.ts                    # 重构为使用中间件
├── rankingApi.ts                 # 重构为使用中间件
└── index.ts                      # 更新统一导出
```

## 🔧 详细技术规范

### 1. 统一API Hook规范

#### useUnifiedApi.ts
**文件路径**: `src/hooks/api/useUnifiedApi.ts`

**接口定义**:
```typescript
interface UseUnifiedApiConfig<T> {
  immediate?: boolean;
  retry?: RetryConfig;
  cache?: CacheConfig;
  polling?: PollingConfig;
  healthCheck?: boolean;
  transform?: (data: any) => T;
  onSuccess?: (data: T) => void;
  onError?: (error: ApiError) => void;
  onStart?: () => void;
  onFinally?: () => void;
}

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
}

interface CacheConfig {
  enabled?: boolean;
  ttl?: number;
  key?: string;
}

interface PollingConfig {
  enabled?: boolean;
  interval?: number;
  stopOnError?: boolean;
}

export function useUnifiedApi<T>(
  apiFunction: () => Promise<T>,
  config: UseUnifiedApiConfig<T> = {}
): UseUnifiedApiReturn<T>;
```

**核心功能**:
- 统一所有API调用场景
- 插件化功能扩展
- 配置驱动的行为控制
- 完整的生命周期管理

#### API配置规范
**文件路径**: `src/hooks/api/configs/apiConfigs.ts`
```typescript
export const API_PRESETS = {
  IMMEDIATE: {
    immediate: true,
    retry: { maxRetries: 3 },
    cache: { enabled: true, ttl: 300000 }
  },
  MANUAL: {
    immediate: false,
    retry: { maxRetries: 1 }
  },
  POLLING: {
    immediate: true,
    polling: { enabled: true, interval: 5000 },
    cache: { enabled: false }
  },
  HEALTH_CHECK: {
    immediate: true,
    healthCheck: true,
    retry: { maxRetries: 5, exponentialBackoff: true }
  }
} as const;

export type ApiPresetType = keyof typeof API_PRESETS;
```

### 2. 中间件系统规范

#### 核心API服务类
**文件路径**: `src/services/api/core/ApiService.ts`
```typescript
interface ApiServiceConfig {
  baseUrl?: string;
  timeout?: number;
  defaultHeaders?: Record<string, string>;
  middleware?: Middleware[];
}

interface Middleware {
  name: string;
  execute: (context: MiddlewareContext, next: () => Promise<any>) => Promise<any>;
  order: number;
}

interface MiddlewareContext {
  request: RequestConfig;
  response?: ApiResponse;
  error?: Error;
  metadata: Record<string, any>;
}

export class ApiService {
  constructor(config: ApiServiceConfig);
  
  async request<T>(config: RequestConfig): Promise<ApiResponse<T>>;
  addMiddleware(middleware: Middleware): void;
  removeMiddleware(name: string): void;
}
```

#### 错误处理中间件
**文件路径**: `src/services/api/middleware/errorHandlingMiddleware.ts`
```typescript
interface ErrorHandlingConfig {
  logErrors?: boolean;
  transformErrors?: boolean;
  retryOnError?: boolean;
}

export function createErrorHandlingMiddleware(
  config: ErrorHandlingConfig = {}
): Middleware {
  return {
    name: 'errorHandling',
    order: 100,
    execute: async (context, next) => {
      try {
        return await next();
      } catch (error) {
        const apiError = ApiErrorHandler.createApiError(error);
        
        if (config.logErrors) {
          ApiErrorHandler.logError(apiError, context.metadata.serviceName);
        }
        
        context.error = apiError;
        throw apiError;
      }
    }
  };
}
```

#### 认证中间件
**文件路径**: `src/services/api/middleware/authMiddleware.ts`
```typescript
interface AuthConfig {
  token: string;
  headerName?: string;
  tokenPrefix?: string;
}

export function createAuthMiddleware(config: AuthConfig): Middleware {
  return {
    name: 'auth',
    order: 10,
    execute: async (context, next) => {
      const headerName = config.headerName || 'xc-token';
      const token = config.tokenPrefix 
        ? `${config.tokenPrefix} ${config.token}`
        : config.token;
      
      context.request.headers = {
        ...context.request.headers,
        [headerName]: token
      };
      
      return next();
    }
  };
}
```

### 3. 服务重构规范

#### navigationApi.ts 重构
**文件路径**: `src/services/api/navigationApi.ts`
```typescript
// 重构前的类方法转换为配置化调用
export class NavigationApiService {
  private static apiService = new ApiService({
    middleware: [
      createAuthMiddleware({ token: NAVIGATION_API_CONFIG.token }),
      createErrorHandlingMiddleware({ logErrors: true }),
      createLoggingMiddleware({ serviceName: 'NavigationApi' }),
      createCacheMiddleware({ defaultTtl: 300000 })
    ]
  });

  static async getTableStructure(baseId: string): Promise<ApiResponse<TableStructureResponse>> {
    return this.apiService.request({
      url: `https://noco.ohvfx.com/api/v2/meta/bases/${baseId}/tables`,
      method: 'GET',
      metadata: { 
        cacheKey: `table_structure_${baseId}`,
        deduplicationKey: `table_structure_${baseId}`
      }
    });
  }
}
```

### 4. 配置统一规范

#### 统一API配置
**文件路径**: `src/services/api/config/apiConfig.ts`
```typescript
export const UNIFIED_API_CONFIG = {
  AUTH: {
    TOKEN: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
    HEADER_NAME: 'xc-token'
  },
  DEFAULTS: {
    TIMEOUT: 30000,
    RETRY_COUNT: 3,
    CACHE_TTL: 300000,
    REQUEST_LIMIT: 500
  },
  ENDPOINTS: {
    BASE_URL: 'https://noco.ohvfx.com/api/v2',
    RACE_TABLE: 'm19dww1xfzsfipk',
    RACE_VIEW: 'vwoimmnq6pws8pso'
  }
} as const;
```

## 🔄 迁移策略

### 阶段1: 核心架构建设 (6小时)
1. 创建统一API Hook架构
2. 实现中间件系统
3. 建立配置管理体系
4. 创建类型定义

### 阶段2: Hook层重构 (4小时)
1. 重构useApi为配置化调用
2. 简化useRaceApi模块
3. 更新useTimeRaceRanking
4. 保持向后兼容性

### 阶段3: 服务层重构 (4小时)
1. 重构所有API服务使用中间件
2. 统一错误处理和配置
3. 实现请求去重和缓存
4. 优化日志记录
