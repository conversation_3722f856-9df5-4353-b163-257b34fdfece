# 代码清理和配置优化实施计划

## 📋 实施概览
**目标**: 清理未使用导入和统一配置管理
**预计节省**: 150行代码
**实施风险**: 低风险
**预计时间**: 3小时 (清理2小时 + 配置1小时)

## 🗂️ 文件结构规划

### 配置统一化
```
src/config/
├── global/
│   ├── apiConfig.ts              # 统一API配置
│   ├── uiConfig.ts               # UI相关配置
│   ├── featureConfig.ts          # 功能开关配置
│   └── index.ts                  # 全局配置导出
├── environments/
│   ├── development.ts            # 开发环境配置
│   ├── production.ts             # 生产环境配置
│   ├── test.ts                   # 测试环境配置
│   └── index.ts                  # 环境配置导出
├── constants/
│   ├── apiConstants.ts           # API相关常量
│   ├── uiConstants.ts            # UI相关常量
│   ├── businessConstants.ts      # 业务相关常量
│   └── index.ts                  # 常量导出
└── index.ts                      # 配置系统总导出
```

### 清理工具
```
scripts/
├── code-cleanup/
│   ├── unused-imports-cleaner.js # 未使用导入清理器
│   ├── dead-code-detector.js     # 死代码检测器
│   ├── config-consolidator.js    # 配置整合器
│   └── cleanup-runner.js         # 清理任务运行器
└── quality-tools/
    ├── eslint-config-custom.js   # 自定义ESLint配置
    ├── prettier-config.js        # Prettier配置
    └── quality-checker.js        # 代码质量检查器
```

## 🔧 详细技术规范

### 1. 统一配置系统

#### 全局API配置
**文件路径**: `src/config/global/apiConfig.ts`
```typescript
interface ApiConfig {
  auth: {
    token: string;
    headerName: string;
  };
  endpoints: {
    baseUrl: string;
    raceTable: string;
    raceView: string;
  };
  defaults: {
    timeout: number;
    retryCount: number;
    cacheTimeout: number;
    requestLimit: number;
  };
}

export const API_CONFIG: ApiConfig = {
  auth: {
    token: process.env.VITE_API_TOKEN || 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
    headerName: 'xc-token'
  },
  endpoints: {
    baseUrl: process.env.VITE_API_BASE_URL || 'https://noco.ohvfx.com/api/v2',
    raceTable: 'm19dww1xfzsfipk',
    raceView: 'vwoimmnq6pws8pso'
  },
  defaults: {
    timeout: 30000,
    retryCount: 3,
    cacheTimeout: 300000,
    requestLimit: 500
  }
};
```

#### UI配置
**文件路径**: `src/config/global/uiConfig.ts`
```typescript
interface UiConfig {
  skeleton: {
    animationDuration: number;
    defaultHeight: string;
    backgroundColor: string;
    borderRadius: string;
  };
  polling: {
    defaultInterval: number;
    maxInterval: number;
    minInterval: number;
  };
  device: {
    defaultCount: number;
    maxCount: number;
  };
}

export const UI_CONFIG: UiConfig = {
  skeleton: {
    animationDuration: 2000,
    defaultHeight: 'auto',
    backgroundColor: 'gray-100',
    borderRadius: 'medium'
  },
  polling: {
    defaultInterval: 5000,
    maxInterval: 30000,
    minInterval: 1000
  },
  device: {
    defaultCount: 17,
    maxCount: 50
  }
};
```

### 2. 未使用导入清理规范

#### ESLint配置增强
**文件路径**: `scripts/quality-tools/eslint-config-custom.js`
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  plugins: [
    '@typescript-eslint',
    'unused-imports',
    'import'
  ],
  rules: {
    // 未使用导入检测
    'unused-imports/no-unused-imports': 'error',
    'unused-imports/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_'
      }
    ],
    
    // 导入顺序规范
    'import/order': [
      'error',
      {
        groups: [
          'builtin',
          'external',
          'internal',
          'parent',
          'sibling',
          'index'
        ],
        'newlines-between': 'always',
        alphabetize: {
          order: 'asc',
          caseInsensitive: true
        }
      }
    ],
    
    // 禁止重复导入
    'import/no-duplicates': 'error',
    
    // TypeScript特定规则
    '@typescript-eslint/no-unused-vars': 'off', // 由unused-imports处理
    '@typescript-eslint/no-explicit-any': 'warn'
  }
};
```

#### 自动清理脚本
**文件路径**: `scripts/code-cleanup/unused-imports-cleaner.js`
```javascript
const fs = require('fs');
const path = require('path');
const { ESLint } = require('eslint');

class UnusedImportsCleaner {
  constructor(options = {}) {
    this.srcDir = options.srcDir || 'src';
    this.extensions = options.extensions || ['.ts', '.tsx', '.js', '.jsx'];
    this.dryRun = options.dryRun || false;
    this.eslint = new ESLint({
      configFile: './scripts/quality-tools/eslint-config-custom.js',
      fix: !this.dryRun
    });
  }

  async cleanUnusedImports() {
    const files = this.getAllFiles(this.srcDir);
    const results = [];

    for (const file of files) {
      try {
        const result = await this.processFile(file);
        if (result.changes > 0) {
          results.push(result);
        }
      } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
      }
    }

    return this.generateReport(results);
  }

  async processFile(filePath) {
    const results = await this.eslint.lintFiles([filePath]);
    const result = results[0];
    
    if (!result) return { file: filePath, changes: 0 };

    const unusedImportErrors = result.messages.filter(
      msg => msg.ruleId === 'unused-imports/no-unused-imports'
    );

    if (!this.dryRun && result.output) {
      fs.writeFileSync(filePath, result.output);
    }

    return {
      file: filePath,
      changes: unusedImportErrors.length,
      messages: unusedImportErrors
    };
  }

  getAllFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getAllFiles(fullPath));
      } else if (stat.isFile() && this.extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }

    return files;
  }

  generateReport(results) {
    const totalFiles = results.length;
    const totalChanges = results.reduce((sum, r) => sum + r.changes, 0);

    return {
      summary: {
        totalFiles,
        totalChanges,
        dryRun: this.dryRun
      },
      details: results,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = UnusedImportsCleaner;
```

### 3. 具体清理目标

#### 需要清理的文件清单
```typescript
// 1. ContentArea.tsx - 注释掉的导入
// 删除第18行: // import { UltimatePKSkeleton } from "../UltimatePKSkeleton";

// 2. FooterSection.tsx - 未使用的类型导入
// 删除第13-14行的未使用Hook类型导入

// 3. useRaceApi/index.ts - 可能未使用的导出
// 检查并清理第28-38行的工厂函数和辅助函数导出

// 4. 多个组件中的motion库导入
// 检查所有组件文件中未使用的motion导入
```

#### 配置常量整合
```typescript
// 将以下重复常量整合到统一配置中:

// 1. API Token (在4个服务中重复)
const API_TOKEN = 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp';

// 2. 请求限制 (在多个API调用中重复)
const REQUEST_LIMIT = 500;

// 3. 超时设置 (在多个服务中重复)
const TIMEOUT = 30000;

// 4. 缓存时间 (在多个地方重复)
const CACHE_TTL = 300000;
```

## 🔄 实施步骤

### 阶段1: 配置系统建设 (1小时)
1. 创建统一配置目录结构
2. 实现全局配置文件
3. 建立环境变量支持
4. 创建常量定义文件

### 阶段2: 自动化清理工具 (1小时)
1. 配置增强的ESLint规则
2. 实现自动清理脚本
3. 创建质量检查工具
4. 建立CI集成配置

### 阶段3: 执行清理任务 (1小时)
1. 运行未使用导入清理
2. 整合重复配置常量
3. 更新所有引用
4. 验证清理结果

## 📊 预期清理成果

### 清理统计
- **未使用导入**: 预计清理25+个文件中的50+行导入
- **重复常量**: 整合15+个重复定义
- **注释代码**: 清理10+行注释掉的代码
- **总计节省**: 约150行代码

### 质量提升
- 建立自动化代码质量检查
- 统一配置管理体系
- 预防未来的代码冗余
- 提高代码可维护性
