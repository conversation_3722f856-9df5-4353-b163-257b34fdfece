# 骨架屏组件重构实施计划

## 📋 实施概览
**目标**: 将6个重复的骨架屏组件重构为分层组合模式
**预计节省**: 450行代码
**实施风险**: 低风险
**预计时间**: 4小时

## 🗂️ 文件结构规划

### 新建文件
```
src/components/common/skeleton/
├── SkeletonBase.tsx              # 基础骨架屏组件
├── SkeletonBase.css              # 基础样式
├── areas/
│   ├── SkeletonTitle.tsx         # 标题区域组件
│   ├── SkeletonContent.tsx       # 内容区域组件
│   ├── SkeletonGrid.tsx          # 网格区域组件
│   ├── SkeletonNavigation.tsx    # 导航区域组件
│   └── index.ts                  # 区域组件导出
├── presets/
│   ├── HomePageSkeleton.tsx      # 首页骨架屏预设
│   ├── QuestionSkeleton.tsx      # 题目骨架屏预设
│   ├── RankingSkeleton.tsx       # 排名骨架屏预设
│   ├── RuleIntroSkeleton.tsx     # 规则骨架屏预设
│   ├── SafetyPublicClassSkeleton.tsx  # 安全课程骨架屏预设
│   ├── UltimatePKSkeleton.tsx    # 终极PK骨架屏预设
│   └── index.ts                  # 预设组件导出
├── types.ts                      # 类型定义
├── constants.ts                  # 常量定义
└── index.ts                      # 统一导出
```

### 修改文件
```
src/components/
├── HomePageSkeleton.tsx          # 重构为使用新架构
├── QuestionSkeleton.tsx          # 重构为使用新架构
├── RankingSkeleton.tsx           # 重构为使用新架构
├── RuleIntroSkeleton.tsx         # 重构为使用新架构
├── SafetyPublicClassSkeleton.tsx # 重构为使用新架构
├── UltimatePKSkeleton.tsx        # 重构为使用新架构
└── layout/ContentArea.tsx        # 更新导入路径
```

## 🔧 详细技术规范

### 1. SkeletonBase.tsx 规范
**文件路径**: `src/components/common/skeleton/SkeletonBase.tsx`

**接口定义**:
```typescript
interface SkeletonBaseProps {
  className?: string;
  animated?: boolean;
  height?: string;
  interactive?: boolean;
  status?: "loading" | "error" | "success";
  fadeEffect?: "fade-in" | "fade-out" | null;
  children: React.ReactNode;
}

export function SkeletonBase(props: SkeletonBaseProps): JSX.Element;
```

**核心功能**:
- 统一的CSS类名构建逻辑
- 标准化的View容器配置
- 通用的动画和状态管理
- 子组件渲染支持

### 2. 区域组件规范

#### SkeletonTitle.tsx
**文件路径**: `src/components/common/skeleton/areas/SkeletonTitle.tsx`
```typescript
interface SkeletonTitleProps {
  width?: string;
  height?: string;
  marginBottom?: string;
}

export function SkeletonTitle(props: SkeletonTitleProps): JSX.Element;
```

#### SkeletonContent.tsx
**文件路径**: `src/components/common/skeleton/areas/SkeletonContent.tsx`
```typescript
interface SkeletonContentProps {
  lines?: number;
  height?: string;
  spacing?: string;
}

export function SkeletonContent(props: SkeletonContentProps): JSX.Element;
```

#### SkeletonGrid.tsx
**文件路径**: `src/components/common/skeleton/areas/SkeletonGrid.tsx`
```typescript
interface SkeletonGridProps {
  rows?: number;
  columns?: string[];
  gap?: string;
  areas?: string;
}

export function SkeletonGrid(props: SkeletonGridProps): JSX.Element;
```

#### SkeletonNavigation.tsx
**文件路径**: `src/components/common/skeleton/areas/SkeletonNavigation.tsx`
```typescript
interface SkeletonNavigationProps {
  buttonCount?: number;
  buttonWidth?: string;
  justifyContent?: string;
}

export function SkeletonNavigation(props: SkeletonNavigationProps): JSX.Element;
```

### 3. 预设组件规范

#### HomePageSkeleton.tsx (重构版)
**文件路径**: `src/components/common/skeleton/presets/HomePageSkeleton.tsx`
```typescript
interface HomePageSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {}

export function HomePageSkeleton(props: HomePageSkeletonProps): JSX.Element {
  return (
    <SkeletonBase {...props}>
      <SkeletonTitle height="size-600" marginBottom="size-400" />
      <SkeletonContent height="size-3000" />
    </SkeletonBase>
  );
}
```

#### QuestionSkeleton.tsx (重构版)
**文件路径**: `src/components/common/skeleton/presets/QuestionSkeleton.tsx`
```typescript
interface QuestionSkeletonProps extends Omit<SkeletonBaseProps, 'children'> {
  showRankingArea?: boolean;
  rankingRowCount?: number;
}

export function QuestionSkeleton(props: QuestionSkeletonProps): JSX.Element;
```

### 4. 类型定义规范
**文件路径**: `src/components/common/skeleton/types.ts`
```typescript
export interface SkeletonBaseProps {
  className?: string;
  animated?: boolean;
  height?: string;
  interactive?: boolean;
  status?: "loading" | "error" | "success";
  fadeEffect?: "fade-in" | "fade-out" | null;
  children: React.ReactNode;
}

export interface SkeletonAreaProps {
  width?: string;
  height?: string;
  marginBottom?: string;
}

export type SkeletonStatus = "loading" | "error" | "success";
export type SkeletonFadeEffect = "fade-in" | "fade-out" | null;
```

### 5. 常量定义规范
**文件路径**: `src/components/common/skeleton/constants.ts`
```typescript
export const SKELETON_DEFAULTS = {
  ANIMATED: true,
  HEIGHT: "auto",
  INTERACTIVE: false,
  STATUS: "loading" as const,
  FADE_EFFECT: null,
} as const;

export const SKELETON_STYLES = {
  BACKGROUND_COLOR: "gray-100",
  BORDER_RADIUS: "medium",
  PADDING: "size-300",
  ITEM_BACKGROUND: "gray-300",
  ITEM_BORDER_RADIUS: "small",
} as const;
```

## 🔄 迁移策略

### 阶段1: 基础架构创建
1. 创建skeleton目录结构
2. 实现SkeletonBase组件
3. 实现所有区域组件
4. 创建类型定义和常量

### 阶段2: 预设组件实现
1. 实现所有预设组件
2. 确保与原组件功能一致
3. 添加单元测试

### 阶段3: 原组件重构
1. 逐个重构原骨架屏组件
2. 更新所有导入引用
3. 删除重复代码

### 阶段4: 清理和优化
1. 删除旧的重复文件
2. 优化样式和性能
3. 更新文档
