# 代码冗余优化总体实施检查清单

## 📋 项目概览
**总预计时间**: 21小时
**总预计节省**: 1600行代码
**实施风险**: 可控 (低-中风险)
**质量提升**: 20%

## 🎯 实施优先级和时间分配

| 阶段 | 任务 | 时间 | 风险 | 收益 | 依赖关系 |
|------|------|------|------|------|----------|
| P0 | 骨架屏组件重构 | 4h | 低 | 高 | 无 |
| P1 | 代码清理优化 | 3h | 低 | 中 | 无 |
| P2 | API Hook统一 | 6h | 中 | 高 | 无 |
| P3 | API服务中间件 | 8h | 中 | 中 | P2完成后 |

## ✅ 详细实施检查清单

### 阶段P0: 骨架屏组件重构 (4小时)

#### 步骤1: 基础架构创建 (1.5小时)
- [ ] 1.1 创建目录结构 `src/components/common/skeleton/`
- [ ] 1.2 创建 `SkeletonBase.tsx` 基础组件
  - 文件路径: `src/components/common/skeleton/SkeletonBase.tsx`
  - 实现统一的属性接口 `SkeletonBaseProps`
  - 实现CSS类名构建逻辑
  - 实现标准化View容器配置
- [ ] 1.3 创建 `types.ts` 类型定义文件
  - 文件路径: `src/components/common/skeleton/types.ts`
  - 定义所有接口和类型
- [ ] 1.4 创建 `constants.ts` 常量定义文件
  - 文件路径: `src/components/common/skeleton/constants.ts`
  - 定义默认值和样式常量

#### 步骤2: 区域组件实现 (1小时)
- [ ] 2.1 创建 `areas/SkeletonTitle.tsx`
  - 实现标题区域组件
  - 支持宽度、高度、边距配置
- [ ] 2.2 创建 `areas/SkeletonContent.tsx`
  - 实现内容区域组件
  - 支持行数、高度、间距配置
- [ ] 2.3 创建 `areas/SkeletonGrid.tsx`
  - 实现网格区域组件
  - 支持行列、间距、区域配置
- [ ] 2.4 创建 `areas/SkeletonNavigation.tsx`
  - 实现导航区域组件
  - 支持按钮数量、宽度、对齐配置
- [ ] 2.5 创建 `areas/index.ts` 导出文件

#### 步骤3: 预设组件实现 (1小时)
- [ ] 3.1 创建 `presets/HomePageSkeleton.tsx`
  - 使用SkeletonBase + SkeletonTitle + SkeletonContent
- [ ] 3.2 创建 `presets/SafetyPublicClassSkeleton.tsx`
  - 复用HomePageSkeleton的结构
- [ ] 3.3 创建 `presets/QuestionSkeleton.tsx`
  - 扩展支持排名区域显示
- [ ] 3.4 创建 `presets/RankingSkeleton.tsx`
  - 扩展支持动态行数配置
- [ ] 3.5 创建 `presets/RuleIntroSkeleton.tsx`
  - 扩展支持导航按钮
- [ ] 3.6 创建 `presets/UltimatePKSkeleton.tsx`
  - 扩展支持PK布局
- [ ] 3.7 创建 `presets/index.ts` 导出文件

#### 步骤4: 原组件重构和清理 (0.5小时)
- [ ] 4.1 重构 `src/components/HomePageSkeleton.tsx`
  - 替换为使用新的预设组件
- [ ] 4.2 重构 `src/components/QuestionSkeleton.tsx`
- [ ] 4.3 重构 `src/components/RankingSkeleton.tsx`
- [ ] 4.4 重构 `src/components/RuleIntroSkeleton.tsx`
- [ ] 4.5 重构 `src/components/SafetyPublicClassSkeleton.tsx`
- [ ] 4.6 重构 `src/components/UltimatePKSkeleton.tsx`
- [ ] 4.7 更新 `src/components/layout/ContentArea.tsx` 导入路径

### 阶段P1: 代码清理优化 (3小时)

#### 步骤5: 配置系统建设 (1小时)
- [ ] 5.1 创建目录结构 `src/config/`
- [ ] 5.2 创建 `src/config/global/apiConfig.ts`
  - 统一所有API相关配置
  - 支持环境变量覆盖
- [ ] 5.3 创建 `src/config/global/uiConfig.ts`
  - 统一UI相关配置
- [ ] 5.4 创建 `src/config/constants/` 目录和文件
  - 整合所有重复常量定义
- [ ] 5.5 创建 `src/config/index.ts` 统一导出

#### 步骤6: 清理工具配置 (1小时)
- [ ] 6.1 创建 `scripts/quality-tools/eslint-config-custom.js`
  - 配置unused-imports插件
  - 配置import顺序规则
- [ ] 6.2 创建 `scripts/code-cleanup/unused-imports-cleaner.js`
  - 实现自动清理脚本
- [ ] 6.3 安装必要的依赖包
  - `npm install --save-dev eslint-plugin-unused-imports`
- [ ] 6.4 更新 `package.json` 添加清理脚本

#### 步骤7: 执行清理任务 (1小时)
- [ ] 7.1 清理 `src/components/layout/ContentArea.tsx`
  - 删除第18行注释掉的UltimatePKSkeleton导入
- [ ] 7.2 清理 `src/components/layout/FooterSection.tsx`
  - 删除未使用的Hook类型导入
- [ ] 7.3 运行自动清理脚本
  - 清理所有未使用的导入
- [ ] 7.4 整合重复配置常量
  - 更新所有API服务使用统一配置
- [ ] 7.5 验证清理结果
  - 确保代码仍能正常编译和运行

### 阶段P2: API Hook统一 (6小时)

#### 步骤8: 统一Hook架构 (3小时)
- [ ] 8.1 创建 `src/hooks/api/` 目录结构
- [ ] 8.2 创建 `src/hooks/api/useUnifiedApi.ts`
  - 实现统一的API Hook接口
  - 支持所有配置选项
- [ ] 8.3 创建 `src/hooks/api/configs/` 配置文件
  - 实现API预设配置
- [ ] 8.4 创建 `src/hooks/api/plugins/` 插件文件
  - 实现重试、缓存、轮询、健康检查插件
- [ ] 8.5 创建 `src/hooks/api/types.ts` 类型定义

#### 步骤9: 现有Hook重构 (3小时)
- [ ] 9.1 重构 `src/hooks/useApi.ts`
  - 使用统一架构实现
  - 保持向后兼容性
- [ ] 9.2 重构 `src/hooks/useRaceApi/useRaceApi.ts`
  - 简化为配置化调用
  - 移除重复的变体函数
- [ ] 9.3 重构 `src/hooks/useTimeRaceRanking.ts`
  - 使用统一API Hook
- [ ] 9.4 更新所有使用这些Hook的组件
- [ ] 9.5 测试所有API调用功能

### 阶段P3: API服务中间件 (8小时)

#### 步骤10: 中间件系统建设 (4小时)
- [ ] 10.1 创建 `src/services/api/middleware/` 目录
- [ ] 10.2 创建 `src/services/api/core/ApiService.ts`
  - 实现核心API服务类
- [ ] 10.3 创建各种中间件
  - `authMiddleware.ts` - 认证中间件
  - `errorHandlingMiddleware.ts` - 错误处理中间件
  - `loggingMiddleware.ts` - 日志中间件
  - `cacheMiddleware.ts` - 缓存中间件
- [ ] 10.4 创建 `src/services/api/core/MiddlewareManager.ts`
  - 实现中间件管理器

#### 步骤11: 服务重构 (4小时)
- [ ] 11.1 重构 `src/services/api/navigationApi.ts`
  - 使用中间件架构
  - 移除重复的错误处理逻辑
- [ ] 11.2 重构 `src/services/api/questionApi.ts`
- [ ] 11.3 重构 `src/services/api/raceApi.ts`
- [ ] 11.4 重构 `src/services/api/rankingApi.ts`
- [ ] 11.5 更新 `src/services/api/index.ts` 导出
- [ ] 11.6 测试所有API服务功能

## 🔍 质量验证检查清单

### 功能验证
- [ ] 所有骨架屏组件正常显示
- [ ] 所有API调用功能正常
- [ ] 所有Hook功能保持一致
- [ ] 错误处理机制正常工作
- [ ] 缓存和重试功能正常

### 代码质量验证
- [ ] ESLint检查通过，无未使用导入
- [ ] TypeScript编译无错误
- [ ] 所有测试用例通过
- [ ] 代码覆盖率保持或提升
- [ ] 性能指标无明显下降

### 文档更新
- [ ] 更新组件使用文档
- [ ] 更新API调用文档
- [ ] 更新开发指南
- [ ] 更新架构文档

## 📊 成功指标

### 量化指标
- [ ] 代码行数减少1600行 (目标达成率 ≥ 90%)
- [ ] 重复代码率降低至 < 5%
- [ ] 构建时间保持或减少
- [ ] 包大小保持或减少

### 质量指标
- [ ] ESLint错误数量为0
- [ ] TypeScript错误数量为0
- [ ] 代码可维护性指数提升
- [ ] 团队开发效率提升

## ⚠️ 风险控制措施

### 备份策略
- [ ] 创建功能分支进行重构
- [ ] 每个阶段完成后创建标签
- [ ] 保留原始代码备份

### 回滚计划
- [ ] 如果P0阶段出现问题，回滚到原始骨架屏组件
- [ ] 如果P2阶段出现问题，保持原有Hook架构
- [ ] 如果P3阶段出现问题，保持原有服务架构

### 测试策略
- [ ] 每个阶段完成后进行完整功能测试
- [ ] 关键路径的自动化测试
- [ ] 性能回归测试
