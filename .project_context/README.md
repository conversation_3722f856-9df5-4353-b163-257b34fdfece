# 项目上下文文档

## 概述
本目录包含中控端项目的完整上下文信息，用于支持AI助手进行精确的代码分析和重构。

## 文档结构

### 核心文档
- `PROJECT_OVERVIEW.md` - 项目总体概览和架构
- `TECH_STACK.md` - 技术栈和依赖分析
- `CODE_STRUCTURE.md` - 代码结构和组织方式
- `COMPONENT_INVENTORY.md` - 组件和模块清单
- `DEPENDENCY_MAP.md` - 依赖关系映射

### 分析数据
- `code_analysis/` - 代码分析结果
- `redundancy_analysis/` - 冗余代码分析数据
- `metrics/` - 代码质量指标

## 维护说明
- 本目录由AI助手自动维护
- 每次重大代码变更后应更新相关文档
- 保持文档与实际代码同步

## 最后更新
2025-07-30 - 初始创建
