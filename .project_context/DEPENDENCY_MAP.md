# 依赖关系映射

## 核心依赖链分析

### 1. 组件层依赖关系

#### ContentArea.tsx - 核心依赖节点
**作为最复杂的组件，导入了大量依赖**:
```
ContentArea.tsx
├── React核心依赖
├── Adobe React Spectrum组件 (8个)
├── 业务组件 (12个)
│   ├── TimeRaceRankingContent
│   ├── HomePageSkeleton
│   ├── HomePageContent
│   ├── RuleIntroContent
│   ├── QuestionContent
│   ├── AudioPlayer
│   ├── UltimatePKContent
│   ├── SafetyPublicClassSkeleton
│   ├── SafetyPublicClassContent
│   └── 其他组件...
├── 配置模块 (2个)
└── 类型定义
```

#### DynamicComponentRenderer.tsx - 配置依赖节点
```
DynamicComponentRenderer.tsx
├── React核心
├── 配置模块 (完整导入)
│   ├── getComponentConfig
│   ├── getButtonGroupsByType
│   ├── getButtonGroupsByTypeWithContext
│   ├── findNavigationNode
│   ├── navigationNodes
│   └── 类型定义 (5个)
├── API类型
└── 样式文件
```

### 2. Hooks层依赖关系

#### useRaceApi - 核心业务Hook
```
useRaceApi/
├── useRaceApi.ts (主文件)
│   ├── React Hooks
│   ├── useApi Hook (基础依赖)
│   ├── API服务函数 (5个)
│   ├── 模块化组件
│   │   ├── factories.ts
│   │   ├── helpers.ts
│   │   └── cache.ts
│   └── 类型定义
├── types.ts (类型定义)
├── factories.ts (工厂函数)
├── helpers.ts (辅助函数)
├── cache.ts (缓存管理)
└── index.ts (导出)
```

#### useAppStateManager - 状态协调器
```
useAppStateManager.ts
├── React Hooks
├── useRaceApi (完整依赖)
├── useQuestionNavigation
├── 状态设置器接口
├── 日志函数
└── MQTT集成接口
```

### 3. Services层依赖关系

#### API服务依赖链
```
services/api/
├── index.ts (统一导出)
│   ├── 导出62个函数和类型
│   ├── 4个业务API模块
│   ├── 错误处理模块
│   └── 工具模块
├── client.ts (HTTP客户端基础)
├── 业务API模块
│   ├── navigationApi.ts
│   │   ├── httpClient依赖
│   │   ├── 错误处理依赖
│   │   ├── 适配器依赖
│   │   └── 验证器依赖
│   ├── questionApi.ts
│   ├── raceApi.ts
│   └── rankingApi.ts
└── 工具模块
    ├── requestDeduplicator.ts
    ├── simplifiedDataService.ts
    └── tableStructureCache.ts
```

#### MQTT服务依赖链
```
services/mqtt/
├── MQTTService.ts (核心服务)
│   ├── mqtt库依赖
│   ├── 类型定义依赖
│   └── 配置接口依赖
├── MQTTMemoryManager.ts
│   └── MQTTService依赖
├── types.ts (独立类型定义)
└── index.ts (导出)
```

## 重复依赖分析

### 1. React Spectrum组件重复导入
**高频导入的组件**:
- `View`, `Flex` - 在多个组件中重复导入
- `Button`, `ActionButton` - 按钮相关组件重复
- `Text`, `Heading` - 文本组件重复
- `ProgressBar`, `ProgressCircle` - 进度组件重复

### 2. 配置模块重复依赖
**重复导入的配置**:
- `getComponentConfig` - 在多个组件中导入
- `navigationNodes` - 导航配置重复引用
- `ButtonGroupConfig` - 按钮配置重复导入

### 3. API服务重复依赖
**重复的服务调用**:
- `httpClient` - 在所有API模块中导入
- `ApiErrorHandler` - 错误处理重复导入
- `createDataFormatError` - 错误创建函数重复

### 4. 类型定义重复导入
**高频导入的类型**:
- `ApiError`, `ApiResponse` - API相关类型
- `NavigationNode` - 导航类型
- `ProcessedRaceItem` - 赛事数据类型
- `LogFunction` - 日志函数类型

## 循环依赖风险点

### 1. Hook间循环依赖
```
useAppStateManager → useRaceApi → useApi
useTimeRaceRanking → useRaceApi
useMQTTIntegration → useAppStateManager (潜在风险)
```

### 2. 配置模块循环依赖
```
componentConfigurations → navigationConfigurations
buttonGroupConfigurations → componentConfigurations
```

### 3. API服务循环依赖
```
simplifiedDataService → navigationApi
navigationApi → requestDeduplicator
requestDeduplicator → client
```

## 未使用导入检测

### 1. 组件层未使用导入
**骨架屏组件**:
- 多个骨架屏组件导入了相同的React Spectrum组件但可能未全部使用
- `motion`库在某些组件中导入但未使用

### 2. Hook层未使用导入
**useRaceApi模块**:
- `factories.ts`, `helpers.ts`中可能存在未使用的导入
- 类型定义文件中的某些类型可能未被引用

### 3. Services层未使用导入
**API服务**:
- `adapters/`, `utils/`, `validators/`子目录中可能存在未使用的模块
- 某些错误处理函数可能未被实际使用

## 优化建议

### 1. 创建通用导入模块
```typescript
// common/imports.ts
export {
  View, Flex, Button, ActionButton,
  Text, Heading, ProgressBar
} from '@adobe/react-spectrum';
```

### 2. 合并相似的Hook
```typescript
// 合并API相关的Hook变体
// 减少useApi和useRaceApi的重复变体函数
```

### 3. 统一错误处理
```typescript
// 创建统一的错误处理中间件
// 减少各API服务中的重复错误处理逻辑
```

### 4. 优化类型导入
```typescript
// 创建统一的类型导出文件
// 减少分散的类型导入
```
