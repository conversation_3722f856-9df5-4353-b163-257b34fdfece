# 骨架屏组件解决方案设计

## 🎯 问题概述
6个骨架屏组件存在85%的代码重复，需要统一化设计。

## 💡 解决方案对比

### 方案A: 单一通用组件 + 配置驱动
**设计理念**: 创建一个高度可配置的通用骨架屏组件

**优点**:
- 最大程度减少代码重复（减少90%冗余）
- 统一的维护入口
- 配置化管理，易于扩展

**缺点**:
- 配置复杂度较高
- 可能过度工程化
- 类型安全性需要额外处理

**实现概要**:
```typescript
interface SkeletonConfig {
  layout: 'simple' | 'question' | 'ranking' | 'pk' | 'rules';
  areas: SkeletonArea[];
  customProps?: Record<string, any>;
}

interface SkeletonArea {
  id: string;
  type: 'title' | 'content' | 'grid' | 'navigation';
  props: SkeletonAreaProps;
}
```

### 方案B: 分层组合模式
**设计理念**: 创建基础组件 + 专用组合组件

**优点**:
- 平衡了复用性和专用性
- 类型安全性更好
- 渐进式重构，风险较低

**缺点**:
- 仍有部分代码重复
- 需要维护多个组件

**实现概要**:
```typescript
// 基础组件
<SkeletonBase {...commonProps}>
  <SkeletonTitle />
  <SkeletonContent />
</SkeletonBase>

// 专用组合
<QuestionSkeleton>
  <SkeletonBase>
    <SkeletonTitle />
    <SkeletonContent />
    <SkeletonRanking />
  </SkeletonBase>
</QuestionSkeleton>
```

### 方案C: 工厂模式 + 预设配置
**设计理念**: 使用工厂函数生成不同类型的骨架屏

**优点**:
- 高度灵活性
- 预设配置简化使用
- 支持运行时动态生成

**缺点**:
- 学习成本较高
- 调试复杂度增加
- 可能影响性能

**实现概要**:
```typescript
const createSkeleton = (type: SkeletonType, options?: SkeletonOptions) => {
  const config = SKELETON_PRESETS[type];
  return <DynamicSkeleton config={mergeConfig(config, options)} />;
};

// 使用方式
const HomeSkeleton = createSkeleton('home');
const QuestionSkeleton = createSkeleton('question', { showRanking: true });
```

## 🏆 推荐方案: 方案B (分层组合模式)

**选择理由**:
1. **风险可控**: 渐进式重构，不会一次性破坏现有功能
2. **平衡性好**: 在代码复用和类型安全之间找到平衡
3. **维护友好**: 既有统一的基础，又保持专用性
4. **扩展性强**: 未来可以轻松添加新的骨架屏类型

**实施策略**:
1. 先创建SkeletonBase基础组件
2. 逐步重构现有组件使用基础组件
3. 提取通用的区域组件（SkeletonTitle, SkeletonContent等）
4. 最后优化和统一样式系统
