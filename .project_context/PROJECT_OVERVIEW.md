# 项目总体概览

## 项目基本信息
- **项目名称**: Nexus Panel (中控端)
- **项目类型**: 实时竞赛管理仪表板
- **技术栈**: React 19.1.0 + TypeScript 5.8.3 + Vite 6.3.5
- **UI框架**: Adobe React Spectrum
- **实时通信**: MQTT 5.13.1

## 项目目标
Nexus Panel 是一个为互动问答/竞赛活动构建的实时竞赛管理仪表板，主要功能包括：
- 实时监控竞赛状态
- 参与者管理
- 动态内容交付
- 竞技游戏场景支持

## 核心功能模块

### 1. 竞赛管理
- 时间赛排名管理
- 终极PK内容管理
- 问答内容管理
- 安全公开课内容

### 2. 实时通信
- MQTT集成和消息管理
- 设备状态监控
- 实时数据同步

### 3. 用户交互
- 导航树视图
- 音频播放器
- 控制台面板
- 侧边栏按钮组

### 4. 数据管理
- API集成和数据获取
- 状态管理
- 配置管理

## 项目结构概览
```
nexus-panel/src/
├── components/          # UI组件
├── hooks/              # 自定义Hooks
├── services/           # 服务层
├── config/             # 配置文件
├── contexts/           # React上下文
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
└── styles/             # 样式文件
```

## 关键技术特性
- 基于React 19的现代前端架构
- TypeScript严格类型检查
- Adobe React Spectrum设计系统
- MQTT实时通信协议
- Vite快速构建和热重载
- ESLint代码质量检查

## 开发和部署
- 开发服务器: `npm run dev`
- 生产构建: `npm run build`
- 代码检查: `npm run lint`
- 文档验证: 多个docs相关脚本

## 文档体系
项目拥有完整的文档体系，位于 `../docs/` 目录，包括架构设计、开发指南、API文档等。
