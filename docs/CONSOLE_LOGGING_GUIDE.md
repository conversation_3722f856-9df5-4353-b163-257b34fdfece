# Console 日志使用规范指南

## 概述

本文档定义了项目中使用原生 console 方法进行日志记录的标准规范，替代之前的自定义日志系统。

## 日志级别映射

### 1. 信息日志 (console.log)
**用途**: 一般信息记录、状态更新、流程追踪
**格式**: `console.log('[模块名] 消息内容', 详细信息对象)`

```javascript
// ✅ 推荐格式
console.log('[API] 配置数据加载成功', {
  leaderCount: 5,
  playerCount: 10,
  timestamp: Date.now()
});

console.log('[MQTT] 连接状态变更', { status: 'connected' });
```

### 2. 警告日志 (console.warn)
**用途**: 警告信息、非致命错误、性能问题
**格式**: `console.warn('[模块名] 警告消息', 详细信息对象)`

```javascript
// ✅ 推荐格式
console.warn('[内存管理] 日志数量接近阈值', {
  current: 800,
  threshold: 1000,
  action: 'cleanup_recommended'
});

console.warn('[API] 请求重试', { attempt: 2, maxRetries: 3 });
```

### 3. 错误日志 (console.error)
**用途**: 错误信息、异常处理、失败状态
**格式**: `console.error('[模块名] 错误消息', 错误对象或详细信息)`

```javascript
// ✅ 推荐格式
console.error('[API] 请求失败', {
  error: error.message,
  code: error.code,
  url: requestUrl,
  timestamp: Date.now()
});

console.error('[MQTT] 连接断开', { reason: 'network_error' });
```

## 环境差异化配置

### 开发环境
- 所有级别的日志都会输出
- 包含详细的调试信息
- 支持对象展开和格式化

### 生产环境
- 仅输出 console.warn 和 console.error
- 简化日志信息，避免敏感数据泄露
- 可通过环境变量控制日志级别

```javascript
// 环境控制示例
const isDevelopment = process.env.NODE_ENV === 'development';

if (isDevelopment) {
  console.log('[调试] 详细状态信息', debugData);
}

// 错误日志在所有环境都输出
console.error('[错误] 关键错误信息', errorData);
```

## 最佳实践

### 1. 模块标识
- 始终在日志消息前添加模块标识 `[模块名]`
- 使用一致的模块命名：API、MQTT、UI、内存管理等

### 2. 结构化数据
- 使用对象传递详细信息，而不是字符串拼接
- 包含时间戳、操作类型等关键信息

### 3. 错误处理
- 错误日志必须包含足够的上下文信息
- 保留原始错误对象的关键信息

### 4. 性能考虑
- 避免在高频调用的函数中使用详细日志
- 生产环境下最小化日志输出

## 迁移对照表

| 原日志级别 | 新方法 | 说明 |
|-----------|--------|------|
| info | console.log | 一般信息 |
| success | console.log | 成功状态（添加成功标识） |
| warning | console.warn | 警告信息 |
| error | console.error | 错误信息 |
| send | console.log | MQTT发送（添加发送标识） |
| get | console.log | 数据获取（添加获取标识） |

## 调试工具使用

### 浏览器开发者工具
1. **控制台过滤**: 使用浏览器控制台的过滤功能按级别查看日志
2. **搜索功能**: 使用模块标识快速定位相关日志
3. **时间戳**: 利用浏览器自动添加的时间戳进行时序分析

### 生产环境监控
1. **错误收集**: 考虑集成错误监控服务（如 Sentry）
2. **日志聚合**: 可选择集成日志聚合服务
3. **性能监控**: 使用浏览器性能工具替代自定义性能日志

## 示例代码

### API 请求日志
```javascript
// 请求开始
console.log('[API] 开始请求', { url, method, timestamp: Date.now() });

try {
  const response = await fetch(url);
  console.log('[API] 请求成功', { status: response.status, url });
} catch (error) {
  console.error('[API] 请求失败', { 
    error: error.message, 
    url, 
    timestamp: Date.now() 
  });
}
```

### MQTT 消息日志
```javascript
// 消息发送
console.log('[MQTT] 发送消息', { topic, messageType, timestamp: Date.now() });

// 消息接收
console.log('[MQTT] 接收消息', { topic, dataType, timestamp: Date.now() });

// 连接错误
console.error('[MQTT] 连接失败', { error: error.message, brokerUrl });
```

### 状态变更日志
```javascript
// 状态更新
console.log('[状态] 导航切换', { 
  from: previousKey, 
  to: selectedKey, 
  timestamp: Date.now() 
});

// 配置加载
console.log('[配置] 动态配置加载完成', {
  configType: 'buttonGroup',
  itemCount: items.length,
  timestamp: Date.now()
});
```

## 注意事项

1. **避免敏感信息**: 不要在日志中输出密码、token等敏感信息
2. **控制日志量**: 避免在循环或高频函数中输出大量日志
3. **保持一致性**: 团队成员应遵循统一的日志格式
4. **及时清理**: 定期清理不必要的调试日志

---

*本指南将随着项目发展持续更新和完善*
